{"version": 3, "file": "Kids.js", "sourceRoot": "", "sources": ["../../../../src/core/clients/Kids.ts"], "names": [], "mappings": ";;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,8BAA8B,CAAC;AACpF,OAAO,EAAE,cAAc,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5E,OAAO,uBAAuB,MAAM,wDAAwD,CAAC;AAE7F,OAAO,EACL,cAAc,EAAE,YAAY,EAC5B,cAAc,EAAE,cAAc,EAC/B,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,uBAAuB,EAAE,MAAM,4BAA4B,CAAC;AAIrE,MAAqB,IAAI;IAGvB,YAAY,OAAgB;QAF5B,gCAAkB;QAGhB,uBAAA,IAAI,iBAAY,OAAO,MAAA,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACG,MAAM,CAAC,KAAa;;YACxB,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,qBAAS,CAAC,OAAO,CAAC,OAAO,CAClD,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CACvE,CAAC;YACF,OAAO,IAAI,MAAM,CAAC,uBAAA,IAAI,qBAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACrD,CAAC;KAAA;IAED;;;OAGG;IACG,OAAO,CAAC,QAAgB;;;YAC5B,MAAM,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC;gBAC1C,GAAG,EAAE,MAAA,uBAAA,IAAI,qBAAS,CAAC,MAAM,0CAAE,GAAG;gBAC9B,MAAM,EAAE,QAAQ;gBAChB,QAAQ;aACT,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC;gBACtC,QAAQ;gBACR,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,uBAAA,IAAI,qBAAS,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAC3F,MAAM,aAAa,GAAG,uBAAA,IAAI,qBAAS,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YACrF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAE,eAAe,EAAE,aAAa,CAAE,CAAC,CAAC;YAEvE,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAErC,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,uBAAA,IAAI,qBAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;;KAC5D;IAED;;;OAGG;IACG,UAAU,CAAC,UAAkB;;YACjC,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,qBAAS,CAAC,OAAO,CAAC,OAAO,CAClD,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,SAAS,EAAE,UAAU;gBACrB,MAAM,EAAE,QAAQ;aACjB,CAAC,CACH,CAAC;YACF,OAAO,IAAI,OAAO,CAAC,uBAAA,IAAI,qBAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACtD,CAAC;KAAA;IAED;;OAEG;IACG,WAAW;;YACf,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,qBAAS,CAAC,OAAO,CAAC,OAAO,CAClD,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,SAAS,EAAE,aAAa;gBACxB,MAAM,EAAE,QAAQ;aACjB,CAAC,CACH,CAAC;YACF,OAAO,IAAI,QAAQ,CAAC,uBAAA,IAAI,qBAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACvD,CAAC;KAAA;IAED;;;;;OAKG;IACG,YAAY,CAAC,UAAkB;;;YACnC,IAAI,CAAC,uBAAA,IAAI,qBAAS,CAAC,SAAS;gBAC1B,MAAM,IAAI,cAAc,CAAC,kDAAkD,CAAC,CAAC;YAE/E,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;YACpF,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,qBAAS,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,iBAAiB,CAAE,CAAC;YACvG,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;YAC1D,MAAM,cAAc,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC;YACzE,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;YACxD,MAAM,IAAI,GAAG,MAAA,UAAU,CAAC,aAAa,0CAAE,OAAO,CAAC,uBAAuB,CAAC,CAAC;YAExE,IAAI,CAAC,IAAI;gBACP,MAAM,IAAI,cAAc,CAAC,0DAA0D,CAAC,CAAC;YAEvF,yEAAyE;YACzE,MAAM,SAAS,GAAkB,EAAE,CAAC;YAEpC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;gBACtB,IAAI,CAAC,CAAA,MAAA,GAAG,CAAC,YAAY,0CAAE,UAAU,CAAA,EAAE;oBACjC,GAAG,CAAC,UAAU,CAAC,uBAAA,IAAI,qBAAS,CAAC,OAAO,CAAC,CAAC;oBACtC,8CAA8C;oBAC9C,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC;iBAC1C;aACF;YAED,OAAO,SAAS,CAAC;;KAClB;CACF;;eAvGoB,IAAI"}