const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const { AttachmentBuilder } = require('discord.js');
let createCanvas = null;
let loadImage = null;
let registerFont = null;
let sharp = null;

try {
    // Optional dependency: canvas
    const canvasLib = require('canvas');
    createCanvas = canvasLib.createCanvas;
    loadImage = canvasLib.loadImage;
    registerFont = canvasLib.registerFont;
} catch (e) {
    console.warn('[musicUtils] Optional dependency "canvas" not installed. Image features will be disabled.');
}

try {
    // Optional dependency: sharp
    sharp = require('sharp');
} catch (e) {
    console.warn('[musicUtils] Optional dependency "sharp" not installed. PNG optimization will be skipped.');
}
const path = require('path');
const fs = require('fs');

// Register fonts for canvas (only if canvas is available)
if (registerFont) {
    try {
        registerFont(path.join(__dirname, '../assets/fonts/Roboto-Bold.ttf'), { family: 'Roboto-Bold' });
        registerFont(path.join(__dirname, '../assets/fonts/Roboto-Regular.ttf'), { family: 'Roboto-Regular' });
    } catch (error) {
        console.warn('[musicUtils] Fonts not found, using system fonts');
    }
}

/**
 * Check if user is in a voice channel
 */
function checkVoiceChannel(member) {
    if (!member.voice.channel) {
        return {
            success: false,
            message: '❌ You need to be in a voice channel to use this command.',
            voiceChannel: null
        };
    }

    if (!member.voice.channel.joinable) {
        return {
            success: false,
            message: '❌ I cannot join your voice channel. Please check my permissions.',
            voiceChannel: null
        };
    }

    if (!member.voice.channel.speakable) {
        return {
            success: false,
            message: '❌ I cannot speak in your voice channel. Please check my permissions.',
            voiceChannel: null
        };
    }

    return {
        success: true,
        message: '✅ Voice channel check passed.',
        voiceChannel: member.voice.channel
    };
}

/**
 * Check bot permissions for voice channel
 */
function checkBotVoiceChannel(voiceChannel) {
    const permissions = voiceChannel.permissionsFor(voiceChannel.guild.members.me);

    if (!permissions.has('Connect')) {
        return {
            success: false,
            message: '❌ I need the "Connect" permission to join voice channels.'
        };
    }

    if (!permissions.has('Speak')) {
        return {
            success: false,
            message: '❌ I need the "Speak" permission to play music in voice channels.'
        };
    }

    if (!permissions.has('ViewChannel')) {
        return {
            success: false,
            message: '❌ I need the "View Channel" permission to see your voice channel.'
        };
    }

    if (!permissions.has('SendMessages')) {
        return {
            success: false,
            message: '❌ I need the "Send Messages" permission to send messages in this channel.'
        };
    }

    if (!permissions.has('EmbedLinks')) {
        return {
            success: false,
            message: '❌ I need the "Embed Links" permission to show music information.'
        };
    }

    if (!permissions.has('AttachFiles')) {
        return {
            success: false,
            message: '❌ I need the "Attach Files" permission to send images and files.'
        };
    }

    return {
        success: true,
        message: '✅ Bot permissions check passed.'
    };
}

/**
 * Create music embed for various events
 */
async function createMusicEmbed(options) {
    const {
        type,
        track,
        tracks,
        user,
        queue,
        position,
        shuffle,
        source,
        lyrics,
        translation,
        sentiment
    } = options;

    const embed = new EmbedBuilder()
        .setColor('#1DB954')
        .setTimestamp();

    switch (type) {
        case 'trackAdded':
            embed.setTitle('🎵 Track Added to Queue')
                .setDescription(`**[${track.title}](${track.url})**`)
                .addFields(
                    { name: 'Artist', value: track.author, inline: true },
                    { name: 'Duration', value: track.duration, inline: true },
                    { name: 'Position in Queue', value: `#${position}`, inline: true },
                    { name: 'Requested by', value: user.toString(), inline: true }
                )
                .setThumbnail(track.thumbnail)
                .setFooter({ text: `Queue size: ${queue.getSize()} tracks` });
            break;

        case 'playlistAdded':
            embed.setTitle('🎵 Playlist Added to Queue')
                .setDescription(`Added **${tracks.length} tracks** to the queue`)
                .addFields(
                    { name: 'Source', value: source.charAt(0).toUpperCase() + source.slice(1), inline: true },
                    { name: 'Shuffled', value: shuffle ? 'Yes' : 'No', inline: true },
                    { name: 'Requested by', value: user.toString(), inline: true },
                    { name: 'Total Duration', value: calculateTotalDuration(tracks), inline: true },
                    { name: 'Queue Size', value: `${queue.getSize()} tracks`, inline: true }
                )
                .setThumbnail(tracks[0]?.thumbnail || 'https://i.imgur.com/placeholder.png')
                .setFooter({ text: 'Use /queue to see all tracks' });
            break;

        case 'nowPlaying':
            embed.setTitle('🎶 Now Playing')
                .setDescription(`**[${track.title}](${track.url})**`)
                .addFields(
                    { name: 'Artist', value: track.author, inline: true },
                    { name: 'Duration', value: track.duration, inline: true },
                    { name: 'Requested by', value: track.requestedBy?.toString() || 'Unknown', inline: true },
                    { name: 'Volume', value: `${queue.node.volume}%`, inline: true },
                    { name: 'Filters', value: queue.filters.ffmpeg.size > 0 ? Array.from(queue.filters.ffmpeg).join(', ') : 'None', inline: true }
                )
                .setThumbnail(track.thumbnail)
                .setFooter({ text: `Progress: ${formatProgress(queue.node.getTimestamp())}` });
            break;

        case 'queue':
            const queueTracks = queue.tracks.toArray();
            const currentPage = options.page || 1;
            const tracksPerPage = 10;
            const startIndex = (currentPage - 1) * tracksPerPage;
            const endIndex = startIndex + tracksPerPage;
            const pageTracks = queueTracks.slice(startIndex, endIndex);

            embed.setTitle(`🎵 Music Queue - ${queueTracks.length} tracks`)
                .setDescription(pageTracks.map((track, index) => 
                    `${startIndex + index + 1}. **[${track.title}](${track.url})** - ${track.author} (${track.duration})`
                ).join('\n') || 'No tracks in queue')
                .addFields(
                    { name: 'Now Playing', value: queue.currentTrack ? `**[${queue.currentTrack.title}](${queue.currentTrack.url})**` : 'Nothing', inline: false },
                    { name: 'Total Duration', value: calculateTotalDuration(queueTracks), inline: true },
                    { name: 'Volume', value: `${queue.node.volume}%`, inline: true },
                    { name: 'Filters', value: queue.filters.ffmpeg.size > 0 ? Array.from(queue.filters.ffmpeg).join(', ') : 'None', inline: true }
                )
                .setFooter({ text: `Page ${currentPage} of ${Math.ceil(queueTracks.length / tracksPerPage)}` });
            break;

        case 'lyrics':
            embed.setTitle('📝 Lyrics')
                .setDescription(`**${track.title}** by ${track.author}`)
                .addFields(
                    { name: 'Lyrics', value: lyrics.length > 1024 ? lyrics.substring(0, 1021) + '...' : lyrics, inline: false }
                )
                .setThumbnail(track.thumbnail)
                .setFooter({ text: 'Powered by Genius & Musixmatch' });

            if (translation) {
                embed.addFields({ name: 'Translation', value: translation.length > 1024 ? translation.substring(0, 1021) + '...' : translation, inline: false });
            }

            if (sentiment) {
                embed.addFields({ name: 'Sentiment Analysis', value: `Mood: ${sentiment.mood}, Score: ${sentiment.score}`, inline: false });
            }
            break;

        case 'filters':
            embed.setTitle('🎛️ Audio Filters')
                .setDescription('Available audio filters')
                .addFields(
                    { name: 'Active Filters', value: queue.filters.ffmpeg.size > 0 ? Array.from(queue.filters.ffmpeg).join(', ') : 'None', inline: false },
                    { name: 'Available Filters', value: 'bassboost, nightcore, vaporwave, pop, soft, treblebass, 8d, karaoke, vibrato, tremolo', inline: false }
                )
                .setFooter({ text: 'Use /filter to apply filters' });
            break;

        default:
            embed.setTitle('🎵 Music')
                .setDescription('Music information');
    }

    return embed;
}

/**
 * Create music control buttons
 */
function createMusicControls() {
    return new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('music_previous')
                .setLabel('⏮️ Previous')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('music_pause_resume')
                .setLabel('⏸️ Pause')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('music_stop')
                .setLabel('⏹️ Stop')
                .setStyle(ButtonStyle.Danger),
            new ButtonBuilder()
                .setCustomId('music_skip')
                .setLabel('⏭️ Skip')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('music_queue')
                .setLabel('📋 Queue')
                .setStyle(ButtonStyle.Secondary)
        );
}

/**
 * Create filter selection menu
 */
function createFilterMenu() {
    return new ActionRowBuilder()
        .addComponents(
            new StringSelectMenuBuilder()
                .setCustomId('music_filter_select')
                .setPlaceholder('Select audio filters')
                .addOptions([
                    { label: 'Bass Boost', value: 'bassboost', description: 'Enhance bass frequencies' },
                    { label: 'Nightcore', value: 'nightcore', description: 'Speed up and pitch up' },
                    { label: 'Vaporwave', value: 'vaporwave', description: 'Slow down and pitch down' },
                    { label: 'Pop', value: 'pop', description: 'Pop music enhancement' },
                    { label: 'Soft', value: 'soft', description: 'Soft music enhancement' },
                    { label: 'Treblebass', value: 'treblebass', description: 'Enhance treble and bass' },
                    { label: '8D Audio', value: '8d', description: '8D audio effect' },
                    { label: 'Karaoke', value: 'karaoke', description: 'Remove vocals' },
                    { label: 'Vibrato', value: 'vibrato', description: 'Add vibrato effect' },
                    { label: 'Tremolo', value: 'tremolo', description: 'Add tremolo effect' }
                ])
        );
}

/**
 * Create source selection menu
 */
function createSourceMenu() {
    return new ActionRowBuilder()
        .addComponents(
            new StringSelectMenuBuilder()
                .setCustomId('music_source_select')
                .setPlaceholder('Select music source')
                .addOptions([
                    { label: 'YouTube', value: 'youtube', description: 'Search YouTube for music' },
                    { label: 'Spotify', value: 'spotify', description: 'Search Spotify for music' },
                    { label: 'Apple Music', value: 'apple', description: 'Search Apple Music for music' },
                    { label: 'SoundCloud', value: 'soundcloud', description: 'Search SoundCloud for music' },
                    { label: 'Deezer', value: 'deezer', description: 'Search Deezer for music' },
                    { label: 'Tidal', value: 'tidal', description: 'Search Tidal for music' },
                    { label: 'Bandcamp', value: 'bandcamp', description: 'Search Bandcamp for music' },
                    { label: 'Radio', value: 'radio', description: 'Listen to radio stations' }
                ])
        );
}

/**
 * Create waveform image for audio visualization
 */
async function createWaveform(track) {
    try {
        if (!createCanvas) {
            // canvas not available, skip waveform generation gracefully
            return null;
        }
        const canvas = createCanvas(800, 200);
        const ctx = canvas.getContext('2d');

        // Background
        ctx.fillStyle = '#1a1a1a';
        ctx.fillRect(0, 0, 800, 200);

        // Generate waveform data (simulated)
        const waveform = generateWaveformData();

        // Draw waveform
        ctx.strokeStyle = '#1DB954';
        ctx.lineWidth = 2;
        ctx.beginPath();

        for (let i = 0; i < waveform.length; i++) {
            const x = (i / waveform.length) * 800;
            const y = 100 + (waveform[i] * 80);
            
            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }

        ctx.stroke();

        // Add track info
        ctx.fillStyle = '#ffffff';
        ctx.font = '16px Roboto-Bold';
        ctx.fillText(track.title, 20, 30);
        
        ctx.fillStyle = '#cccccc';
        ctx.font = '14px Roboto-Regular';
        ctx.fillText(track.author, 20, 55);

        // Convert to buffer
        const buffer = canvas.toBuffer('image/png');

        // Optimize with sharp if available
        let outBuffer = buffer;
        if (sharp) {
            try {
                outBuffer = await sharp(buffer)
                    .resize(800, 200)
                    .png({ quality: 80 })
                    .toBuffer();
            } catch (e) {
                // Fallback to non-optimized buffer
                outBuffer = buffer;
            }
        }

        return new AttachmentBuilder(outBuffer, { name: 'waveform.png' });
    } catch (error) {
        console.error('Error creating waveform:', error);
        return null;
    }
}

/**
 * Generate simulated waveform data
 */
function generateWaveformData() {
    const data = [];
    for (let i = 0; i < 200; i++) {
        data.push(Math.sin(i * 0.1) * Math.random() * 0.8);
    }
    return data;
}

/**
 * Calculate total duration of tracks
 */
function calculateTotalDuration(tracks) {
    let totalSeconds = 0;
    
    tracks.forEach(track => {
        const duration = parseDuration(track.duration);
        totalSeconds += duration;
    });

    return formatDuration(totalSeconds);
}

/**
 * Parse duration string to seconds
 */
function parseDuration(duration) {
    // Handle different duration formats
    if (typeof duration === 'number') {
        return duration; // Already in seconds
    }

    if (!duration || typeof duration !== 'string') {
        return 0; // Default to 0 if invalid
    }

    const parts = duration.split(':');
    let seconds = 0;

    if (parts.length === 3) {
        seconds = parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
    } else if (parts.length === 2) {
        seconds = parseInt(parts[0]) * 60 + parseInt(parts[1]);
    } else if (parts.length === 1) {
        seconds = parseInt(parts[0]);
    }

    return isNaN(seconds) ? 0 : seconds;
}

/**
 * Format duration from seconds
 */
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

/**
 * Format progress timestamp
 */
function formatProgress(timestamp) {
    if (!timestamp) return '0:00';
    
    const current = formatDuration(timestamp.current);
    const total = formatDuration(timestamp.total);
    
    return `${current} / ${total}`;
}

/**
 * Format number with commas
 */
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * Create progress bar
 */
function createProgressBar(current, total, length = 20) {
    const progress = current / total;
    const filled = Math.round(progress * length);
    const empty = length - filled;
    
    return '█'.repeat(filled) + '░'.repeat(empty);
}

module.exports = {
    checkVoiceChannel,
    checkBotVoiceChannel,
    createMusicEmbed,
    createMusicControls,
    createFilterMenu,
    createSourceMenu,
    createWaveform,
    calculateTotalDuration,
    parseDuration,
    formatDuration,
    formatProgress,
    formatNumber,
    createProgressBar
};
