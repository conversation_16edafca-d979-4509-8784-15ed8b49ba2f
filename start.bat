@echo off
title Ultra-Premium Discord Music Bot
echo Select an option:
echo 1. Deploy commands only
echo 2. Start bot only
echo 3. Deploy commands and start bot
choice /c 123 /n /m "Enter your choice (1-3): "

if %errorlevel%==1 (
    echo Deploying commands...
    node src/deploy-commands.js
    echo Commands deployed successfully!
    pause
)
if %errorlevel%==2 (
    echo Starting bot...
    node src/index.js
    pause
)
if %errorlevel%==3 (
    echo Deploying commands...
    node src/deploy-commands.js
    timeout /t 3 /nobreak
    echo Starting bot...
    node src/index.js
    pause
)
