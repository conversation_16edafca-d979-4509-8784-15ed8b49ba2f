const { Player } = require('discord-player');
const { SpotifyExtractor } = require('@discord-player/extractor');
const { YouTubeExtractor } = require('@discord-player/extractor');
const { SoundCloudExtractor } = require('@discord-player/extractor');
const { AppleMusicExtractor } = require('@discord-player/extractor');
const { DeezerExtractor } = require('@discord-player/extractor');
const { AttachmentExtractor } = require('@discord-player/extractor');

// Lavalink related imports
const { Shoukaku, Connectors } = require('shoukaku');
const { Lavasfy } = require('lavasfy');

const logger = require('../utils/logger');

class AudioService {
    constructor(bot) {
        this.bot = bot;
        this.player = null;
        this.shoukaku = null;
        this.lavasfy = null;
        this.nodes = new Map();
        this.stats = {
            totalNodes: 0,
            connectedNodes: 0,
            totalPlayers: 0,
            memoryUsage: 0,
            cpuUsage: 0,
            uptime: 0
        };
        
        this.initializeAudioSystem();
    }

    async initializeAudioSystem() {
        try {
            // Initialize Discord Player
            this.player = new Player(this.bot, {
                ytdlOptions: {
                    quality: 'highestaudio',
                    highWaterMark: 1 << 25,
                    filter: 'audioonly'
                },
                connectionTimeout: 30000,
                leaveOnEmpty: true,
                leaveOnEmptyCooldown: 300000, // 5 minutes
                leaveOnEnd: true,
                leaveOnEndCooldown: 300000, // 5 minutes
                leaveOnStop: true,
                leaveOnStopCooldown: 300000, // 5 minutes
                autoSelfDeaf: true,
                initialVolume: 80,
                bufferingTimeout: 3000,
                spotifyBridge: true,
                appleBridge: true,
                deezerBridge: true,
                disableVolume: false,
                disableEqualizer: false,
                disableFilters: false,
                disableResampler: false,
                disableBiquad: false,
                disableFfmpeg: false,
                enableLive: true,
                enableDiscordEncodings: true
            });

            // Initialize Shoukaku for Lavalink (only if enabled)
            if (this.bot.config.lavalink.enabled !== false && this.bot.config.lavalink.nodes.length > 0) {
                this.shoukaku = new Shoukaku(
                    new Connectors.DiscordJS(this.bot),
                    this.bot.config.lavalink.nodes,
                    {
                        moveOnDisconnect: false,
                        resumable: true,
                        resumableTimeout: 30,
                        reconnectTries: 2,
                        restTimeout: 10000,
                        userAgent: 'Ultra-Premium Music Bot (https://github.com/yourusername/ultra-premium-music-bot)'
                    }
                );
            } else {
                logger.info('🔇 Lavalink disabled - using Discord Player only');
            }

            // Initialize Lavasfy for Spotify support (optional)
            if (this.bot.config.sources.spotify.clientId && this.bot.config.sources.spotify.clientSecret) {
                try {
                    if (typeof Lavasfy === 'function') {
                        this.lavasfy = new Lavasfy({
                            clientID: this.bot.config.sources.spotify.clientId,
                            clientSecret: this.bot.config.sources.spotify.clientSecret,
                            playlistLoadLimit: 100,
                            albumLoadLimit: 50,
                            searchLimit: 20,
                            audioOnlyResults: true
                        }, this.bot.config.lavalink.nodes);
                    } else {
                        logger.warn('Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.');
                    }
                } catch (e) {
                    logger.warn('Failed to initialize Lavasfy. Spotify URL playback via Lavalink will be disabled.');
                }
            }

            // Register extractors
            await this.registerExtractors();

            // Set up event handlers
            this.setupEventHandlers();

            // Connect to Lavalink nodes (only if enabled)
            if (this.shoukaku) {
                try {
                    await this.connectToNodes();
                } catch (e) {
                    logger.warn('⚠️ Lavalink connection not established. Continuing without Lavalink.');
                }
            } else {
                logger.warn('⚠️ Proceeding without Lavalink connection');
            }

            logger.success('✅ Audio system initialized successfully');
            logger.audioSystem('Audio system initialized', {
                nodes: this.bot.config.lavalink.nodes.length,
                extractors: 'Spotify, YouTube, SoundCloud, Apple Music, Deezer',
                lavasfy: this.lavasfy ? 'Enabled' : 'Disabled'
            });

        } catch (error) {
            logger.error('❌ Error initializing audio system:', error);
            // Do not rethrow to allow the bot to start without full audio capabilities
            // throw error;
        }
    }

    async registerExtractors() {
        try {
            // Load default extractors provided by discord-player
            await this.player.extractors.loadDefault();
            logger.success('✅ Default audio extractors registered');
        } catch (error) {
            logger.error('❌ Error registering extractors:', error);
            throw error;
        }
    }

    setupEventHandlers() {
        // Discord Player events
        this.player.events.on('playerStart', (queue, track) => {
            logger.trackStart(track, queue.guild);
            this.bot.emit('trackStart', queue, track);
        });

        this.player.events.on('playerFinish', (queue, track) => {
            logger.trackEnd(track, queue.guild);
            this.bot.emit('trackEnd', queue, track);
        });

        this.player.events.on('playerError', (queue, error) => {
            logger.error('Player error:', error);
            logger.playerError(error, queue.guild);
            this.bot.emit('playerError', queue, error);
        });

        this.player.events.on('playerEmpty', (queue) => {
            logger.queueEmpty(queue.guild);
            this.bot.emit('queueEmpty', queue);
        });

        this.player.events.on('playerDisconnect', (queue) => {
            logger.voiceChannelLeave(queue.guild, queue.connection.channel.name);
            this.bot.emit('playerDisconnect', queue);
        });

        this.player.events.on('playerMove', (queue, oldChannel, newChannel) => {
            logger.voiceChannelMove(queue.guild, oldChannel.name, newChannel.name);
            this.bot.emit('playerMove', queue, oldChannel, newChannel);
        });

        this.player.events.on('connectionCreate', (queue) => {
            logger.voiceChannelJoin(queue.guild, queue.connection.channel.name);
            this.bot.emit('connectionCreate', queue);
        });

        this.player.events.on('connectionError', (queue, error) => {
            logger.error('Connection error:', error);
            logger.connectionError(error, queue.guild);
            this.bot.emit('connectionError', queue, error);
        });

        // Shoukaku events (only if enabled)
        if (this.shoukaku) {
            this.shoukaku.on('ready', () => {
                logger.success('✅ Shoukaku connected to Lavalink nodes');
                this.updateStats();
            });

            this.shoukaku.on('error', (name, error) => {
                logger.error(`❌ Shoukaku error on node ${name}:`, error);
                this.bot.emit('nodeError', name, error);
            });

            this.shoukaku.on('close', (name, code, reason) => {
                logger.warn(`⚠️ Shoukaku node ${name} closed: ${code} - ${reason}`);
                this.bot.emit('nodeClose', name, code, reason);
            });

            this.shoukaku.on('disconnect', (name, players, moved) => {
                logger.warn(`⚠️ Shoukaku node ${name} disconnected, ${players} players affected, ${moved} moved`);
                this.bot.emit('nodeDisconnect', name, players, moved);
            });

            this.shoukaku.on('debug', (name, info) => {
                logger.debug(`Shoukaku debug ${name}:`, info);
            });

            // Node specific events
            this.shoukaku.on('nodeConnect', (name) => {
                logger.success(`✅ Connected to Lavalink node: ${name}`);
                this.nodes.set(name, { connected: true, lastConnected: Date.now() });
                this.updateStats();
            });

            this.shoukaku.on('nodeDisconnect', (name) => {
                logger.warn(`⚠️ Disconnected from Lavalink node: ${name}`);
                this.nodes.set(name, { connected: false, lastDisconnected: Date.now() });
                this.updateStats();
            });

            this.shoukaku.on('nodeReconnect', (name) => {
                logger.info(`🔄 Reconnecting to Lavalink node: ${name}`);
            });
        }
    }

    async connectToNodes() {
        try {
            if (!this.shoukaku) {
                logger.warn('⚠️ Shoukaku not initialized - skipping node connection');
                return;
            }

            const nodes = this.bot.config.lavalink.nodes;

            if (!nodes || nodes.length === 0) {
                logger.warn('⚠️ No Lavalink nodes configured');
                return;
            }

            logger.info(`🔌 Connecting to ${nodes.length} Lavalink nodes...`);

            // Initialize nodes map
            nodes.forEach(node => {
                this.nodes.set(node.name, {
                    connected: false,
                    url: node.url,
                    auth: node.auth,
                    region: node.region,
                    lastConnected: null,
                    lastDisconnected: null
                });
            });

            // Shoukaku will automatically connect to nodes
            const connected = await new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    logger.warn('Lavalink connection timeout');
                    resolve(false);
                }, 30000);

                this.shoukaku.once('ready', () => {
                    clearTimeout(timeout);
                    resolve(true);
                });

                this.shoukaku.once('error', (name, error) => {
                    clearTimeout(timeout);
                    logger.warn(`Lavalink connection error on node ${name}: ${error.message}`);
                    resolve(false);
                });
            });

            if (connected) {
                logger.success('✅ Connected to Lavalink nodes');
            } else {
                logger.warn('Proceeding without Lavalink connection');
            }
            this.updateStats();

        } catch (error) {
            logger.warn('❌ Error connecting to Lavalink nodes (non-fatal): ' + error.message);
            // Do not throw, allow bot to continue running
        }
    }

    async createPlayer(guild, voiceChannel, textChannel) {
        try {
            const queue = this.player.nodes.create(guild, {
                metadata: {
                    channel: textChannel,
                    voiceChannel: voiceChannel,
                    guild: guild,
                    skipVotes: new Map(),
                    requestedBy: null
                },
                selfDeaf: true,
                volume: 80,
                leaveOnEmpty: true,
                leaveOnEmptyCooldown: 300000,
                leaveOnEnd: true,
                leaveOnEndCooldown: 300000,
                leaveOnStop: true,
                leaveOnStopCooldown: 300000,
                connectionTimeout: 30000,
                bufferingTimeout: 3000,
                spotifyBridge: true,
                appleBridge: true,
                deezerBridge: true,
                disableVolume: false,
                disableEqualizer: false,
                disableFilters: false,
                disableResampler: false,
                disableBiquad: false,
                disableFfmpeg: false,
                enableLive: true,
                enableDiscordEncodings: true
            });

            if (!queue.connection) {
                await queue.connect(voiceChannel);
            }

            logger.playerCreate(guild, voiceChannel.name);
            this.updateStats();

            return queue;

        } catch (error) {
            logger.error('Error creating player:', error);
            throw error;
        }
    }

    async destroyPlayer(guild) {
        try {
            const queue = this.player.nodes.get(guild.id);
            if (queue) {
                await queue.delete();
                logger.playerDestroy(guild);
                this.updateStats();
            }
        } catch (error) {
            logger.error('Error destroying player:', error);
            throw error;
        }
    }

    async getQueue(guild) {
        try {
            return this.player.nodes.get(guild.id);
        } catch (error) {
            logger.error('Error getting queue:', error);
            return null;
        }
    }

    async search(query, options = {}) {
        try {
            const { source = 'youtube', user, guild } = options;
            
            let searchQuery = query;
            let searchEngine = 'youtube';

            // Handle different sources
            switch (source) {
                case 'spotify':
                    if (query.includes('open.spotify.com')) {
                        searchQuery = query;
                        searchEngine = 'spotifySearch';
                    } else {
                        searchQuery = `spsearch:${query}`;
                        searchEngine = 'spotifySearch';
                    }
                    break;
                case 'apple':
                    if (query.includes('music.apple.com')) {
                        searchQuery = query;
                        searchEngine = 'appleMusicSearch';
                    } else {
                        searchQuery = `amsearch:${query}`;
                        searchEngine = 'appleMusicSearch';
                    }
                    break;
                case 'soundcloud':
                    if (query.includes('soundcloud.com')) {
                        searchQuery = query;
                        searchEngine = 'soundcloud';
                    } else {
                        searchQuery = `scsearch:${query}`;
                        searchEngine = 'soundcloud';
                    }
                    break;
                case 'deezer':
                    if (query.includes('deezer.com')) {
                        searchQuery = query;
                        searchEngine = 'deezer';
                    } else {
                        searchQuery = `dzsearch:${query}`;
                        searchEngine = 'deezer';
                    }
                    break;
                case 'youtube':
                default:
                    if (!query.includes('youtube.com') && !query.includes('youtu.be')) {
                        searchQuery = query;
                        searchEngine = 'youtube';
                    }
                    break;
            }

            const result = await this.player.search(searchQuery, {
                requestedBy: user,
                searchEngine: searchEngine
            });

            return result;

        } catch (error) {
            logger.error('Error searching tracks:', error);
            throw error;
        }
    }

    async getSpotifyTrack(url) {
        try {
            if (!this.lavasfy) {
                throw new Error('Spotify support not configured');
            }

            const result = await this.lavasfy.request(url);
            return result;

        } catch (error) {
            logger.error('Error getting Spotify track:', error);
            throw error;
        }
    }

    async getBestNode() {
        try {
            const nodes = this.shoukaku.nodes;
            const connectedNodes = Array.from(nodes.values()).filter(node => node.state === 2); // CONNECTED state

            if (connectedNodes.length === 0) {
                throw new Error('No connected Lavalink nodes available');
            }

            // Find node with least load
            let bestNode = connectedNodes[0];
            let lowestLoad = bestNode.stats.cpu ? bestNode.stats.cpu.systemLoad : 100;

            for (const node of connectedNodes) {
                const load = node.stats.cpu ? node.stats.cpu.systemLoad : 100;
                if (load < lowestLoad) {
                    lowestLoad = load;
                    bestNode = node;
                }
            }

            return bestNode;

        } catch (error) {
            logger.error('Error getting best node:', error);
            throw error;
        }
    }

    updateStats() {
        try {
            const nodes = this.shoukaku.nodes;
            const connectedNodes = Array.from(nodes.values()).filter(node => node.state === 2);
            
            let totalPlayers = 0;
            let totalMemory = 0;
            let totalCpu = 0;

            connectedNodes.forEach(node => {
                if (node.stats) {
                    totalPlayers += node.stats.playingPlayers;
                    totalMemory += node.stats.memory.used;
                    totalCpu += node.stats.cpu ? node.stats.cpu.systemLoad : 0;
                }
            });

            this.stats = {
                totalNodes: nodes.size,
                connectedNodes: connectedNodes.length,
                totalPlayers: totalPlayers,
                memoryUsage: totalMemory,
                cpuUsage: connectedNodes.length > 0 ? totalCpu / connectedNodes.length : 0,
                uptime: this.bot.uptime
            };

        } catch (error) {
            logger.error('Error updating stats:', error);
        }
    }

    getStats() {
        return { ...this.stats };
    }

    getNodeStats() {
        const nodes = this.shoukaku.nodes;
        const nodeStats = {};

        nodes.forEach((node, name) => {
            nodeStats[name] = {
                name: name,
                connected: node.state === 2,
                stats: node.stats,
                region: node.region,
                penalty: node.penalty
            };
        });

        return nodeStats;
    }

    async restartNode(nodeName) {
        try {
            const node = this.shoukaku.nodes.get(nodeName);
            if (!node) {
                throw new Error(`Node ${nodeName} not found`);
            }

            await node.disconnect();
            await node.connect();

            logger.success(`✅ Node ${nodeName} restarted successfully`);
            this.updateStats();

        } catch (error) {
            logger.error(`Error restarting node ${nodeName}:`, error);
            throw error;
        }
    }

    async healthCheck() {
        try {
            const nodes = this.shoukaku.nodes;
            const connectedNodes = Array.from(nodes.values()).filter(node => node.state === 2);
            
            if (connectedNodes.length === 0) {
                return {
                    healthy: false,
                    message: 'No connected Lavalink nodes',
                    nodes: this.getNodeStats()
                };
            }

            // Check if at least one node has low load
            const healthyNodes = connectedNodes.filter(node => {
                const cpuLoad = node.stats.cpu ? node.stats.cpu.systemLoad : 100;
                const memoryLoad = node.stats.memory ? (node.stats.memory.used / node.stats.memory.reservable) * 100 : 100;
                return cpuLoad < 80 && memoryLoad < 80;
            });

            return {
                healthy: healthyNodes.length > 0,
                message: healthyNodes.length > 0 ? 'System healthy' : 'All nodes under high load',
                nodes: this.getNodeStats()
            };

        } catch (error) {
            logger.error('Error performing health check:', error);
            return {
                healthy: false,
                message: `Health check failed: ${error.message}`,
                nodes: this.getNodeStats()
            };
        }
    }
}

// Singleton instance
let audioServiceInstance = null;

const getAudioService = (bot) => {
    if (!audioServiceInstance) {
        audioServiceInstance = new AudioService(bot);
    }
    return audioServiceInstance;
};

module.exports = {
    AudioService,
    getAudioService
};
