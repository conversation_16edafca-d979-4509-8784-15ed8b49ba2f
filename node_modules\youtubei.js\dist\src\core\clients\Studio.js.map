{"version": 3, "file": "Studio.js", "sourceRoot": "", "sources": ["../../../../src/core/clients/Studio.ts"], "names": [], "mappings": ";;AAAA,OAAO,KAAK,KAAK,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AACnF,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AAkBnE,MAAqB,MAAM;IAGzB,YAAY,OAAgB;;QAF5B,kCAAkB;QAGhB,uBAAA,IAAI,mBAAY,OAAO,MAAA,CAAC;IAC1B,CAAC;IAED;;;;;;;OAOG;IACG,YAAY,CAAC,QAAgB,EAAE,MAAkB;;YACrD,IAAI,CAAC,uBAAA,IAAI,uBAAS,CAAC,SAAS;gBAC1B,MAAM,IAAI,cAAc,CAAC,kDAAkD,CAAC,CAAC;YAE/E,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM;gBACtB,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;YAErE,MAAM,OAAO,GAAG,KAAK,CAAC,4BAA4B,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAErE,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,uBAAS,CAAC,OAAO,CAAC,OAAO,CAAC,gCAAgC,EAAE;gBACrF,QAAQ,EAAE,IAAI;gBACd,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;KAAA;IAED;;;;;;;;;;;;;OAaG;IACG,mBAAmB,CAAC,QAAgB,EAAE,QAAoC;;YAC9E,IAAI,CAAC,uBAAA,IAAI,uBAAS,CAAC,SAAS;gBAC1B,MAAM,IAAI,cAAc,CAAC,kDAAkD,CAAC,CAAC;YAE/E,MAAM,OAAO,GAAG,KAAK,CAAC,0BAA0B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAErE,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,uBAAS,CAAC,OAAO,CAAC,OAAO,CAAC,gCAAgC,EAAE;gBACrF,QAAQ,EAAE,IAAI;gBACd,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;KAAA;IAED;;;;;;;OAOG;IACG,MAAM,CAAC,IAAc,EAAE,WAAyC,EAAE;;YACtE,IAAI,CAAC,uBAAA,IAAI,uBAAS,CAAC,SAAS;gBAC1B,MAAM,IAAI,cAAc,CAAC,kDAAkD,CAAC,CAAC;YAE/E,MAAM,YAAY,GAAG,MAAM,uBAAA,IAAI,uDAAsB,MAA1B,IAAI,CAAwB,CAAC;YACxD,MAAM,aAAa,GAAG,MAAM,uBAAA,IAAI,8CAAa,MAAjB,IAAI,EAAc,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAE7E,IAAI,aAAa,CAAC,MAAM,KAAK,gBAAgB;gBAC3C,MAAM,IAAI,cAAc,CAAC,0BAA0B,CAAC,CAAC;YAEvD,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,mDAAkB,MAAtB,IAAI,EAAmB,YAAY,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAErF,OAAO,QAAQ,CAAC;QAClB,CAAC;KAAA;CAuFF;;;QApFG,MAAM,kBAAkB,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC;QAE1F,MAAM,OAAO,GAAG;YACd,gBAAgB,EAAE,kBAAkB;YACpC,iBAAiB,EAAE,aAAa;YAChC,MAAM,EAAE,mFAAmF,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YACnH,2BAA2B,EAAE,aAAa;YAC1C,eAAe,EAAE,UAAU;YAC3B,cAAc,EAAE,MAAM;SACvB,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,uBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAClE,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,SAAS;YACjC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,mCAAmC;gBACnD,uBAAuB,EAAE,OAAO;gBAChC,wBAAwB,EAAE,WAAW;aACtC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE;YACd,MAAM,IAAI,cAAc,CAAC,mCAAmC,CAAC,CAAC;QAEhE,OAAO;YACL,kBAAkB;YAClB,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAW;YACjE,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAW;YAC/D,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAW;YAC7F,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAW;SACrF,CAAC;IACJ,CAAC;sDAEkB,UAAkB,EAAE,IAAc;;QACnD,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,uBAAS,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;YACnE,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,mCAAmC;gBACnD,uBAAuB,EAAE,kBAAkB;gBAC3C,yBAAyB,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC/C,sBAAsB,EAAE,GAAG;aAC5B;YACD,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE;YACd,MAAM,IAAI,cAAc,CAAC,wBAAwB,CAAC,CAAC;QAErD,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAEnC,OAAO,IAAI,CAAC;IACd,CAAC;gEAEuB,YAA+B,EAAE,aAA2B,EAAE,QAAsC;;QAC1H,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,uBAAS,CAAC,OAAO,CAAC,OAAO,CAClD,mBAAmB,CAAC,IAAI,EAAE,mBAAmB,CAAC,KAAK,CAAC;YAClD,WAAW,EAAE;gBACX,kBAAkB,EAAE;oBAClB,EAAE,EAAE,aAAa,CAAC,gBAAgB;iBACnC;aACF;YACD,kBAAkB,EAAE,YAAY,CAAC,kBAAkB;YACnD,gBAAgB,EAAE;gBAChB,KAAK,EAAE;oBACL,SAAS,EAAE,QAAQ,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC,YAAY,EAAE;iBACvD;gBACD,WAAW,EAAE;oBACX,eAAe,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;oBAC3C,cAAc,EAAE,IAAI;iBACrB;gBACD,OAAO,EAAE;oBACP,WAAW,EAAE,QAAQ,CAAC,OAAO,IAAI,SAAS;iBAC3C;gBACD,WAAW,EAAE;oBACX,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B;aACF;YACD,MAAM,EAAE,SAAS;SAClB,CAAC,CACH,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;;eAvKkB,MAAM"}