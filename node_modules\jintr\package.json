{"name": "jintr", "version": "1.2.0", "description": "A tiny JavaScript interpreter written in TypeScript.", "type": "module", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/index.d.ts"]}}, "exports": {".": {"node": {"import": "./dist/index.js", "require": "./bundle/jinter.cjs"}, "types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"test": "npx jest --verbose", "build": "npm run build:esm && npm run build:nodes-map && npm run bundle:node", "lint": "npx eslint ./src/**/*.ts", "lint:fix": "npx eslint --fix ./src/**/*.ts", "build:esm": "npx tsc", "build:nodes-map": "node ./scripts/build-nodes-map.js", "bundle:node": "npx esbuild ./dist/index.js --bundle --outfile=./bundle/jinter.cjs --platform=node --target=node10 --format=cjs --sourcemap --banner:js=\"/* eslint-disable */\" --external:acorn", "prepare": "npm run build", "watch": "npx tsc --watch"}, "author": "LuanRT <<EMAIL>> (https://github.com/LuanRT)", "funding": ["https://github.com/sponsors/LuanRT"], "keywords": ["js", "vm", "typescript", "javascript", "interpreter"], "license": "MIT", "devDependencies": {"@types/estree": "^1.0.0", "@types/jest": "^28.1.8", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "esbuild": "^0.17.15", "eslint": "^8.22.0", "eslint-plugin-tsdoc": "^0.2.16", "glob": "^8.0.3", "jest": "^28.1.3", "ts-jest": "^28.0.8", "typescript": "^4.9.5"}, "dependencies": {"acorn": "^8.8.0"}, "bugs": {"url": "https://github.com/LuanRT/Jinter/issues"}, "homepage": "https://github.com/LuanRT/Jinter#readme"}