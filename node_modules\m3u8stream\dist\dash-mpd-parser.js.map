{"version": 3, "file": "dash-mpd-parser.js", "sourceRoot": "", "sources": ["../src/dash-mpd-parser.ts"], "names": [], "mappings": ";;;;;AAAA,mCAAkC;AAClC,8CAAsB;AACtB,6CAA2C;AAI3C;;GAEG;AACH,MAAqB,aAAc,SAAQ,iBAAQ;IAGjD,YAAY,QAAiB;QAC3B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,aAAG,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAElD,IAAI,OAAsB,CAAC;QAC3B,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,eAA2D,CAAC;QAChE,IAAI,SAAiB,EAAE,MAAc,EAAE,QAAgB,EAAE,OAAiB,CAAC;QAC3E,IAAI,QAAQ,GAIN,EAAE,CAAC;QACT,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,QAAiB,CAAC;QACtB,IAAI,SAAiB,CAAC;QACtB,IAAI,WAAmB,CAAC;QAExB,MAAM,IAAI,GAAG,CAAC,GAAW,EAAU,EAAE;YACnC,MAAM,OAAO,GAAmD;gBAC9D,gBAAgB,EAAE,QAAQ;gBAC1B,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,QAAQ;aACf,CAAC;YACF,OAAO,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;YAChC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,KAAK;oBACR,QAAQ;wBACN,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;4BACrC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClE,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,SAAS,CAAC;oBAC9C,MAAM;gBACR,KAAK,QAAQ;oBACX,oCAAoC;oBACpC,GAAG,GAAG,CAAC,CAAC;oBACR,SAAS,GAAG,IAAI,CAAC;oBACjB,QAAQ,GAAG,CAAC,CAAC;oBACb,MAAM,GAAG,CAAC,CAAC;oBACX,OAAO,GAAG,EAAE,CAAC;oBACb,SAAS,GAAG,CAAC,CAAC;oBACd,WAAW,GAAG,wBAAW,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACtD,MAAM;gBACR,KAAK,aAAa;oBAChB,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC;oBACnD,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC;oBAC7D,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC;oBAC1D,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,IAAI,MAAM,CAAC;oBACpE,MAAM;gBACR,KAAK,iBAAiB;oBACpB,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;oBAClC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC;oBACnD,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC;oBAC7D,MAAM;gBACR,KAAK,iBAAiB,CAAC;gBACvB,KAAK,SAAS;oBACZ,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;oBACpB,MAAM;gBACR,KAAK,GAAG;oBACN,QAAQ,CAAC,IAAI,CAAC;wBACZ,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;wBACrC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;wBACnC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;qBAClC,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,eAAe,CAAC;gBACrB,KAAK,gBAAgB;oBACnB,SAAS,EAAE,CAAC;oBACZ,IAAI,CAAC,QAAQ,EAAE;wBACb,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;qBAC/B;oBACD,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,GAAG,QAAQ,EAAE,CAAC;oBACnD,IAAI,WAAW,EAAE;wBACf,IAAI,WAAW,EAAE;4BACf,QAAQ,IAAI,WAAW,CAAC;yBACzB;wBACD,IAAI,MAAM,EAAE;4BACV,QAAQ,IAAI,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;yBACvC;wBACD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;qBAClC;oBACD,MAAM;gBACR,KAAK,gBAAgB;oBACnB,IAAI,WAAW,EAAE;wBACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;4BAChB,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS;4BAClE,GAAG,EAAE,GAAG;4BACR,IAAI,EAAE,IAAI;4BACV,QAAQ,EAAE,CAAC;yBACZ,CAAC,CAAC;qBACJ;oBACD,MAAM;gBACR,KAAK,YAAY;oBACf,IAAI,WAAW,EAAE;wBACf,WAAW,GAAG,IAAI,CAAC;wBACnB,IAAI,EAAE,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;wBAC1B,IAAI,eAAe,GAAG,CAAC,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,QAAQ,KAAI,QAAQ,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;wBACpE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;4BAChB,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK;4BAC9D,GAAG,EAAE,GAAG,EAAE;4BACV,QAAQ,EAAE,eAAe;yBAC1B,CAAC,CAAC;wBACH,QAAQ,IAAI,eAAe,CAAC;qBAC7B;oBACD,MAAM;aACT;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,GAAS,EAAE;YACvB,IAAI,QAAQ,EAAE;gBAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAAE;YACvC,IAAI,CAAC,WAAW,EAAE;gBAChB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,QAAQ,aAAa,CAAC,CAAC,CAAC;aAC/D;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAClB;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE;YACpC,QAAQ,OAAO,EAAE;gBACf,KAAK,eAAe,CAAC;gBACrB,KAAK,gBAAgB;oBACnB,SAAS,EAAE,CAAC;oBACZ,IAAI,eAAe,IAAI,QAAQ,CAAC,MAAM,EAAE;wBACtC,WAAW,GAAG,IAAI,CAAC;wBACnB,IAAI,eAAe,CAAC,cAAc,EAAE;4BAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gCAChB,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;oCACtC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;gCACpC,GAAG,EAAE,GAAG;gCACR,IAAI,EAAE,IAAI;gCACV,QAAQ,EAAE,CAAC;6BACZ,CAAC,CAAC;yBACJ;wBACD,KAAK,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,QAAQ,EAAE;4BAC7D,YAAY,GAAG,YAAY,GAAG,SAAS,GAAG,IAAI,CAAC;4BAC/C,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;4BACrB,QAAQ,GAAG,IAAI,IAAI,QAAQ,CAAC;4BAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gCAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oCAChB,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;wCACtC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;oCAC3B,GAAG,EAAE,GAAG,EAAE;oCACV,QAAQ,EAAE,YAAY;iCACvB,CAAC,CAAC;gCACH,QAAQ,IAAI,YAAY,CAAC;6BAC1B;yBACF;qBACF;oBACD,IAAI,WAAW,EAAE;wBACf,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACtB,KAAK,EAAE,CAAC;wBACR,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;wBAClC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;qBACnC;oBACD,MAAM;aACT;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;YAC7B,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;gBAC1B,OAAO,GAAG,IAAI,CAAC;aAChB;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,KAAa,EAAE,QAAgB,EAAE,QAAoB;QAC1D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,QAAQ,EAAE,CAAC;IACb,CAAC;CACF;AApLD,gCAoLC"}