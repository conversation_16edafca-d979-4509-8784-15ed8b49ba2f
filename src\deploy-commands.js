const { REST, Routes } = require('discord.js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const { token, clientId } = require('./config/config');

const commands = [];

// Load commands from files
const commandsPath = path.join(__dirname, 'commands');
const commandFolders = fs.readdirSync(commandsPath);

for (const folder of commandFolders) {
    const folderPath = path.join(commandsPath, folder);
    const stat = fs.statSync(folderPath);
    
    if (stat.isDirectory()) {
        const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));
        
        for (const file of commandFiles) {
            const filePath = path.join(folderPath, file);
            const command = require(filePath);
            
            if (command.data) {
                commands.push(command.data.toJSON());
                console.log(`[deploy-commands] Loaded command: ${command.data.name}`);
            } else if (command.slash) {
                commands.push(command.slash.toJSON());
                console.log(`[deploy-commands] Loaded command: ${command.slash.name}`);
            }
        }
    }
}

// Construct and prepare an instance of the REST module
const rest = new REST({ version: '10' }).setToken(token);

// Deploy commands globally (works on all servers)
async function deployCommands() {
    try {
        console.log(`[deploy-commands] Started refreshing ${commands.length} application (/) commands.`);

        // Deploy globally to work on all servers
        const data = await rest.put(
            Routes.applicationCommands(clientId),
            { body: commands },
        );
        console.log(`[deploy-commands] Successfully reloaded ${data.length} global application commands.`);
        return true;
    } catch (error) {
        console.error('[deploy-commands] Error deploying commands:', error);
        return false;
    }
}

// Export the function for use in index.js
module.exports = { deployCommands };

// If this file is run directly, deploy commands
if (require.main === module) {
    deployCommands();
}
