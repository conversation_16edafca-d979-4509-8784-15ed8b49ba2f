{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at new AudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:31:14)\n    at getAudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:576:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:135:33)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:20:33"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:37)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:33"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing services: CommandHandler is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: CommandHandler is not a constructor\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:152:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:20:33"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: CommandHandler is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: CommandHandler is not a constructor\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:152:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:20:33"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:20:24 GMT","request-id":"cb4ee8a2-249b-4a20-a8f0-b9c51ab27e93","server":"envoy","set-cookie":["__Host-device_id=AQCBqC8qh8waddCeQeuI9os8j9OKq36Cr5jrSo-nvN2AkySryNTMyeUH8enuAW7OIXE2s75hIrrcyfXWB5nrh8xtRxILv-wheUQ;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"7"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:20:35"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:35"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:35"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:35"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:35"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:35"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:35"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:35"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing music sources: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at MusicService.initializeSources (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\musicService.js:82:32)","timestamp":"2025-09-13 00:20:35"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:20:35"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at new AudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:31:14)\n    at getAudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:576:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:135:33)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:21:01"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:37)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing services: CommandHandler is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: CommandHandler is not a constructor\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:152:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:21:01"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: CommandHandler is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: CommandHandler is not a constructor\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:152:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:21:01"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:20:50 GMT","request-id":"0cba059d-e666-48cc-8d45-0007aea40f66","server":"envoy","set-cookie":["__Host-device_id=AQD5WFPanE7JWcH4RuGkup0GbpYTrf84Fu-HYeMS4p5GXvusLlFPZuEdpyjHFIrQr12zwRXx7U4MaDUWMB4NciqrM8Zsu0j3kKY;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:21:01"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing music sources: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at MusicService.initializeSources (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\musicService.js:82:32)","timestamp":"2025-09-13 00:21:01"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:01"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at new AudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:31:14)\n    at getAudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:576:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:135:33)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:21:13"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:37)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing services: CommandHandler is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: CommandHandler is not a constructor\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:152:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:21:13"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: CommandHandler is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: CommandHandler is not a constructor\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:152:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:21:13"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:21:02 GMT","request-id":"039df1f7-78b7-4d6d-966c-d8df4bd8e92d","server":"envoy","set-cookie":["__Host-device_id=AQDVErScJfeDTLyvLF-aSpnriaYoxIplV8Qxm3RlcFX1CQe4FmNc5OAPksqiWXUohYcrAHcpahzk42w-mF3taovsFa4PJzV6y64;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"7"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:21:13"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing music sources: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at MusicService.initializeSources (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\musicService.js:82:32)","timestamp":"2025-09-13 00:21:13"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:13"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at new AudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:31:14)\n    at getAudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:576:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:135:33)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:21:20"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:37)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing services: CommandHandler is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: CommandHandler is not a constructor\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:152:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:21:20"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: CommandHandler is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: CommandHandler is not a constructor\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:152:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:21:20"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:21:09 GMT","request-id":"1c772799-36a2-4586-8b00-edff8585cd86","server":"envoy","set-cookie":["__Host-device_id=AQC2igP5dqC-vYUO4I-HVRbtwmChe_yRZ3E3w4jWpw82IMbdL6t35wwa_2pZ8m5IDv1m-rj_jos4JS-yg7PEe-Cg2pKxzsEEFps;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"10"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:21:20"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing music sources: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at MusicService.initializeSources (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\musicService.js:82:32)","timestamp":"2025-09-13 00:21:20"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:20"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at new AudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:31:14)\n    at getAudioService (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:576:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:135:33)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:21:46"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:82:32)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:37)\n    at UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:24)\n    at new UltraPremiumMusicBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:75:14)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:225:13)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:46"}
{"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js"]},"level":"error","message":"❌ ❌ Error initializing services: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js","service":"ultra-premium-music-bot","stack":"Error: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n    at Function._load (node:internal/modules/cjs/loader:1075:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:219:24)\n    at Module.require (node:internal/modules/cjs/loader:1340:12)\n    at require (node:internal/modules/helpers:138:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js:3:51)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:21:46"}
{"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js"]},"level":"error","message":"❌ ❌ Failed to initialize bot: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js","service":"ultra-premium-music-bot","stack":"Error: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n    at Function._load (node:internal/modules/cjs/loader:1075:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:219:24)\n    at Module.require (node:internal/modules/cjs/loader:1340:12)\n    at require (node:internal/modules/helpers:138:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js:3:51)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:21:46"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:21:37 GMT","request-id":"02036f3a-21a7-44d4-99c6-c5f65013f26c","server":"envoy","set-cookie":["__Host-device_id=AQDwzlBaeahNHSx9tRISQRYmTMnCorYHWUaKEGnbXCyYKNToK7enlU3-0cIK8eMN4RJIxXSCixfGDFwF9OQz9MRwFxAh7NcMsns;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"8"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:21:48"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:48"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:49"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:49"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:49"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:49"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:49"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:49"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing music sources: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at MusicService.initializeSources (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\musicService.js:82:32)","timestamp":"2025-09-13 00:21:49"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:21:49"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:05"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:05"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:05"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:05"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:05"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:05"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:05"}
{"error":{},"level":"error","message":"❌ ❌ Error registering extractors: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)","timestamp":"2025-09-13 00:22:06"}
{"error":{},"level":"error","message":"❌ ❌ Error registering extractors: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)\n    at async UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:13)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:22:06"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)","timestamp":"2025-09-13 00:22:06"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)\n    at async UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:13)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:06"}
{"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js"]},"level":"error","message":"❌ ❌ Error initializing services: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js","service":"ultra-premium-music-bot","stack":"Error: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n    at Function._load (node:internal/modules/cjs/loader:1075:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:219:24)\n    at Module.require (node:internal/modules/cjs/loader:1340:12)\n    at require (node:internal/modules/helpers:138:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js:3:51)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:22:09"}
{"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js"]},"level":"error","message":"❌ ❌ Failed to initialize bot: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js","service":"ultra-premium-music-bot","stack":"Error: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n    at Function._load (node:internal/modules/cjs/loader:1075:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:219:24)\n    at Module.require (node:internal/modules/cjs/loader:1340:12)\n    at require (node:internal/modules/helpers:138:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js:3:51)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:22:09"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:21:59 GMT","request-id":"829aed6a-28ab-490c-a36f-c5123ebd86b8","server":"envoy","set-cookie":["__Host-device_id=AQBPNZWwonZBzSwmkltY6HdPk80ckDK0ADLZCSTYmRVAPjzpCSENOpm0HdPKlwEX0Kxl0kAUW-o3kun97Rio9s9A2hbnAMpvpE4;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:22:09"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:09"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:09"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:09"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:09"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:09"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:09"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:09"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing music sources: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at MusicService.initializeSources (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\musicService.js:82:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-09-13 00:22:09"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:09"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"error":{},"level":"error","message":"❌ ❌ Error registering extractors: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)","timestamp":"2025-09-13 00:22:35"}
{"error":{},"level":"error","message":"❌ ❌ Error registering extractors: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)\n    at async UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:13)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:22:35"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)","timestamp":"2025-09-13 00:22:35"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)\n    at async UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:13)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js"]},"level":"error","message":"❌ ❌ Error initializing services: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js","service":"ultra-premium-music-bot","stack":"Error: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n    at Function._load (node:internal/modules/cjs/loader:1075:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:219:24)\n    at Module.require (node:internal/modules/cjs/loader:1340:12)\n    at require (node:internal/modules/helpers:138:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js:3:51)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:22:35"}
{"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js"]},"level":"error","message":"❌ ❌ Failed to initialize bot: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js","service":"ultra-premium-music-bot","stack":"Error: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n    at Function._load (node:internal/modules/cjs/loader:1075:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:219:24)\n    at Module.require (node:internal/modules/cjs/loader:1340:12)\n    at require (node:internal/modules/helpers:138:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js:3:51)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:22:35"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:22:25 GMT","request-id":"0d2eefd5-254b-41d2-b684-610f8e759009","server":"envoy","set-cookie":["__Host-device_id=AQASnOpcFztiVcW4JsBP3b69OiVBkd_KSmdYHIbqExlSvWo1xYaHCi3svRXMzr_1fHcFZJTJm6IbJKhfdFuxRpomxc7p9jnM6Vo;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:22:35"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing music sources: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at MusicService.initializeSources (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\musicService.js:82:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-09-13 00:22:35"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:22:35"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"error":{},"level":"error","message":"❌ ❌ Error registering extractors: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)","timestamp":"2025-09-13 00:23:11"}
{"error":{},"level":"error","message":"❌ ❌ Error registering extractors: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)\n    at async UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:13)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:23:11"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)","timestamp":"2025-09-13 00:23:11"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Cannot read properties of undefined (reading 'identifier')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'identifier')\n    at _ExtractorExecutionContext.register (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord-player\\dist\\index.js:1391:27)\n    at AudioService.registerExtractors (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:137:42)\n    at async AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:101:13)\n    at async UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:136:13)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:11"}
{"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js"]},"level":"error","message":"❌ ❌ Error initializing services: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js","service":"ultra-premium-music-bot","stack":"Error: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n    at Function._load (node:internal/modules/cjs/loader:1075:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:219:24)\n    at Module.require (node:internal/modules/cjs/loader:1340:12)\n    at require (node:internal/modules/helpers:138:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js:3:51)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:23:11"}
{"error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js","C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js"]},"level":"error","message":"❌ ❌ Failed to initialize bot: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js","service":"ultra-premium-music-bot","stack":"Error: Cannot find module 'canvas'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\pause.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\commandHandler.js\n- C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1249:15)\n    at Function._load (node:internal/modules/cjs/loader:1075:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:219:24)\n    at Module.require (node:internal/modules/cjs/loader:1340:12)\n    at require (node:internal/modules/helpers:138:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\utils\\musicUtils.js:3:51)\n    at Module._compile (node:internal/modules/cjs/loader:1565:14)\n    at Object..js (node:internal/modules/cjs/loader:1708:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","timestamp":"2025-09-13 00:23:11"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:23:01 GMT","request-id":"5609e14f-5d5d-4cfd-8995-6445dae2cbe8","server":"envoy","set-cookie":["__Host-device_id=AQBF3OovT5lvbE4NCMhmmD6KdkdVKcBGlqPxJhPyFE9D-rWXQRspLsrvhIRDqcoyb21F4X-Nk_tjG6V3XHal_vgDSZ4k9z0423M;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:23:12"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:12"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:12"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:12"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:12"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:12"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:12"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:12"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing music sources: Lavasfy is not a constructor","service":"ultra-premium-music-bot","stack":"TypeError: Lavasfy is not a constructor\n    at MusicService.initializeSources (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\musicService.js:82:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-09-13 00:23:12"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:12"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:23:59"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:27"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:24:47"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:25:06 GMT","request-id":"a676ab06-8ded-407a-a49a-64221dfa52ed","server":"envoy","set-cookie":["__Host-device_id=AQA-OIgq1ZREWZDffJ8hHg8Ik5-PlfwMEhNVbHhXvqbbJZ-0UN5D59oFs8naao-O-Bec-VDUrobD1uYYUuVkmtSMG_GwujO9rrc;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:25:17"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:17"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Error initializing services: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:25:17"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Failed to initialize bot: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:25:17"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:43"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:43"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:43"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:43"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:43"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:43"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:43"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:44"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:44"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:44"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:25:44"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:26:03 GMT","request-id":"4ea0aa9f-6765-46b7-ae98-47ccc9b9c03b","server":"envoy","set-cookie":["__Host-device_id=AQCoDLVyGy3KAo9W-3yXJiwsfoaAaqXwPMxjapBTxycQ26u72hcDZGNuL00H4tlMkzw3Krb9o9ySpGtSimb4q7NRA5UHbDjvbhQ;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:26:14"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:14"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Error initializing services: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:26:14"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Failed to initialize bot: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:26:14"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:26:30"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:26"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:39"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:28:49"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:28:58 GMT","request-id":"49a33096-36ac-473f-8250-3314817ada85","server":"envoy","set-cookie":["__Host-device_id=AQCqTzCSR8PSzQnZrG5L4Ry-xklxATIs34tpmjJYivH2rL4GFjV371c9PWv1-xvhDrM9L6z6dio2VabuBCyngWgD1aerS7442NU;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:29:09"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:09"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Error initializing services: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:29:09"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Failed to initialize bot: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:29:09"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:19"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:29:32"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:31:39"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:31:58 GMT","request-id":"b42206d9-3ef0-49f1-b4a1-1f1e3562f2a5","server":"envoy","set-cookie":["__Host-device_id=AQCPFOyLwFH8vwPFiFSfb-OVtYPZIIzN_p10XdTbljMYSI4Thb2dCZB63826fxY2X1mJ5GrA3OxSJlGNfFbWzyP7Uuduk2OHZ9s;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:32:09"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ Registered 0 commands globally","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:32:09"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Error initializing services: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:32:09"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Failed to initialize bot: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:32:09"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:39:48"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:40:08 GMT","request-id":"1941c2b7-0409-43dd-be17-61a50aca773e","server":"envoy","set-cookie":["__Host-device_id=AQDMg3ejgCryd4tkfH83syWH20vYM3kdKKiIDykYcT_s_pyHIRjjr-TnzWuadV469bmbi0RTsjf9rx0Ma8J2Soo-uAEpqKCO0jQ;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"7"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:40:18"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:18"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:19"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:40:19"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Error initializing services: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:40:19"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Failed to initialize bot: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:40:19"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:45:58"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:48:41"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:49:00 GMT","request-id":"64065b72-7bb7-4cae-8d79-fa430042aa2f","server":"envoy","set-cookie":["__Host-device_id=AQCf30lNWFT1HFVSrVZyfuydW6NMm72GvdvX24ftpsluEint7RaWReDN7f4UNnDxhWP0BmmXXGDkwJV22FB3QRzWKwpKVx1x8NM;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"7"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:49:11"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ Registered 0 commands globally","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:49:11"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Error initializing services: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:49:11"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Failed to initialize bot: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:49:11"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:51:35"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:05"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:51:55 GMT","request-id":"785f747e-9849-4d93-9096-4b85a8445b74","server":"envoy","set-cookie":["__Host-device_id=AQARaRXtM0YKn5MrOxfekRYmPiFY5RKdpFaAmalfLngYzXrKzw_RFaFISwtZc743DU1tHTPf6uk5k4BpVqRLCJHMyBsyjteh-w4;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:52:06"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:52:06"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Error initializing services: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:52:06"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Failed to initialize bot: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:52:06"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:18"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:53:37"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Fri, 12 Sep 2025 23:53:57 GMT","request-id":"553137bb-d7cc-4567-9147-147689c310fd","server":"envoy","set-cookie":["__Host-device_id=AQCZ3IBX3CPpclcvlpuK88Eq01ZxreH6qxb6FeckHZzkV5Pn0bTgrQCCBqiVI-Jz0uhw3epcA4Bm9i5JAuWxPcTUvbjht6QO1R4;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 00:54:07"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:07"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:08"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 00:54:08"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Error initializing services: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:54:08"}
{"error":{"code":"ENOENT","errno":-4058,"path":"C:\\Users\\<USER>\\Documents\\new music boot\\src\\events","syscall":"scandir"},"level":"error","message":"❌ ❌ Failed to initialize bot: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'","service":"ultra-premium-music-bot","stack":"Error: ENOENT: no such file or directory, scandir 'C:\\Users\\<USER>\\Documents\\new music boot\\src\\events'\n    at Object.readdirSync (node:fs:1502:26)\n    at EventHandler.loadEvents (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:12:33)\n    at UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:158:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:83:13)","timestamp":"2025-09-13 00:54:08"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:04:20"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:10:56"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 00:11:16 GMT","request-id":"f59ecd1d-ca0d-4066-abd2-b7a046bed205","server":"envoy","set-cookie":["__Host-device_id=AQC2ikJKfXRY9PGekhQ_TXDzj6nRm1TUNb4s3nLRyIZObBMQKnw0MaIUa2vygnKIg7J9qLS8R1EM0AyhCOPS6dSWgEzi5IEzpZc;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 01:11:26"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:11:26"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: Used disallowed intents","service":"ultra-premium-music-bot","stack":"Error: Used disallowed intents\n    at WebSocketShard.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:1151:18)\n    at connection.onclose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:688:17)\n    at callListener (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:220:9)\n    at WebSocket.emit (node:events:524:28)\n    at WebSocket.emitClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:272:10)\n    at TLSSocket.socketOnClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1341:15)\n    at TLSSocket.emit (node:events:536:35)\n    at node:net:350:12\n    at TCP.done (node:_tls_wrap:650:7)","timestamp":"2025-09-13 01:11:27"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:18:33"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:20:33"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 00:20:53 GMT","request-id":"f825eb94-9403-473a-be05-c4790bb452e1","server":"envoy","set-cookie":["__Host-device_id=AQB-MUh0QfhCuTknF6NeRSKuULdqjQkMnAZsSUR2563bLjTbRJcQHcU0F7332t4ySywQJ3mhiOWnk144xX67jo9rRK8A3-dkpYg;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"11"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 01:21:03"}
{"level":"warn","message":"⚠️ ⚠️ Apple Music API disabled due to missing `apple-music` package.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:21:03"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: Used disallowed intents","service":"ultra-premium-music-bot","stack":"Error: Used disallowed intents\n    at WebSocketShard.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:1151:18)\n    at connection.onclose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:688:17)\n    at callListener (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:220:9)\n    at WebSocket.emit (node:events:524:28)\n    at WebSocket.emitClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:272:10)\n    at TLSSocket.socketOnClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1341:15)\n    at TLSSocket.emit (node:events:536:35)\n    at node:net:350:12\n    at TCP.done (node:_tls_wrap:650:7)","timestamp":"2025-09-13 01:21:04"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:34:35"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:35:30"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 00:35:49 GMT","request-id":"564d0ae9-c774-4ce1-98da-687c2734b911","server":"envoy","set-cookie":["__Host-device_id=AQAA_9q0XXaOwv8Npi47RBfUkiB-Xk8EQD9wHF_6YC0qxTFkaS3fzoxk5s3XNcOShkfaoGQmST2_D_sM6Db3ey5ObVmitNLr6kA;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:00"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:01"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:01"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:01"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:01"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:36:01"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: Used disallowed intents","service":"ultra-premium-music-bot","stack":"Error: Used disallowed intents\n    at WebSocketShard.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:1151:18)\n    at connection.onclose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:688:17)\n    at callListener (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:220:9)\n    at WebSocket.emit (node:events:524:28)\n    at WebSocket.emitClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:272:10)\n    at TLSSocket.socketOnClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1341:15)\n    at TLSSocket.emit (node:events:536:35)\n    at node:net:350:12\n    at TCP.done (node:_tls_wrap:650:7)","timestamp":"2025-09-13 01:36:02"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:04"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:10"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 00:38:23 GMT","request-id":"0a30578b-1167-4d11-8845-97b4d1c98f60","server":"envoy","set-cookie":["__Host-device_id=AQBzUg409NxP6bVycq1sB9Ofm6EToi-_jFI8gZn6fnWv-xCUZT9Qmxh3oXCYB26if39byxfg1u67Klsu4gRlq2SlIgYo1nXLza4;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"7"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:38:34"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: Used disallowed intents","service":"ultra-premium-music-bot","stack":"Error: Used disallowed intents\n    at WebSocketShard.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:1151:18)\n    at connection.onclose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:688:17)\n    at callListener (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:220:9)\n    at WebSocket.emit (node:events:524:28)\n    at WebSocket.emitClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:272:10)\n    at TLSSocket.socketOnClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1341:15)\n    at TLSSocket.emit (node:events:536:35)\n    at node:net:350:12\n    at TCP.done (node:_tls_wrap:650:7)","timestamp":"2025-09-13 01:38:35"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:08"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 00:41:28 GMT","request-id":"e3cec749-198f-4667-b31e-20ef09d72510","server":"envoy","set-cookie":["__Host-device_id=AQBJ-ExcziJE6RAfYQI1mq1wJ3VT9h8zQOeWRDhkJPx-pqNp1mieBfdNGYiNZDPWZcWRC5Ay28z09mbV20Kj3Oq6L3jBjigtQ5w;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:38"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:39"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:39"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:41:39"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: Used disallowed intents","service":"ultra-premium-music-bot","stack":"Error: Used disallowed intents\n    at WebSocketShard.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:1151:18)\n    at connection.onclose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:688:17)\n    at callListener (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:220:9)\n    at WebSocket.emit (node:events:524:28)\n    at WebSocket.emitClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:272:10)\n    at TLSSocket.socketOnClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1341:15)\n    at TLSSocket.emit (node:events:536:35)\n    at node:net:350:12\n    at TCP.done (node:_tls_wrap:650:7)","timestamp":"2025-09-13 01:41:39"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:14"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:18"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:18"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:18"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:18"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:18"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:18"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:18"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:19"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:19"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:19"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:19"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 00:43:38 GMT","request-id":"cd406fa2-445f-427c-87f2-47dfdb236e09","server":"envoy","set-cookie":["__Host-device_id=AQCfSjnNdkdSbBbb2d-DRTme9v74Pxgsc0l4vpRsh1pv835zWzMeDNDL3OqLK_rz9ictNEbgBK_TDSlEFXhcnGUX5ZgxUouSq1Q;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:43:49"}
{"error":{},"level":"error","message":"❌ ❌ Failed to initialize bot: Used disallowed intents","service":"ultra-premium-music-bot","stack":"Error: Used disallowed intents\n    at WebSocketShard.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:1151:18)\n    at connection.onclose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:688:17)\n    at callListener (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\event-target.js:220:9)\n    at WebSocket.emit (node:events:524:28)\n    at WebSocket.emitClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:272:10)\n    at TLSSocket.socketOnClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1341:15)\n    at TLSSocket.emit (node:events:536:35)\n    at node:net:350:12\n    at TCP.done (node:_tls_wrap:650:7)","timestamp":"2025-09-13 01:43:50"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"info","message":"🔌 Connecting to 4 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:48:47"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":4,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 00:49:07 GMT","request-id":"a0464866-027f-4b2c-a4a7-d50bc20e1f36","server":"envoy","set-cookie":["__Host-device_id=AQDNISz1zeS5NFyMuivphf8aG8BFuc7M_c0FkJ_a7hVmuMuLWTclhwYYyEWutixkw_aAaWCtxShQmy-A2-oSJ5XIpmsqHJK-FCo;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:17"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:18"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:18"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:18"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:18"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:18"}
{"level":"info","message":"✅ Bot successfully initialized and logged in!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:18"}
{"level":"info","message":"✅ Logged in as new music bot#4798!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:19"}
{"level":"info","message":"🎯 Bot is in 1 guild(s)","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:19"}
{"level":"info","message":"✅ 🎉 Bot is ready to serve!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:19"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"primary"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('primary')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('primary')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 01:49:21"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"backup"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('backup')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('backup')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 01:49:21"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"europe"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('europe')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('europe')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 01:49:24"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"asia"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('asia')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('asia')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 01:49:24"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:27"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:27"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:27"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:27"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:27"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:27"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node backup: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:27"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node backup: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:27"}
{"level":"warn","message":"⚠️ Shoukaku node backup closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:27"}
{"level":"warn","message":"⚠️ Shoukaku node backup closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:27"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node europe: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:29"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node europe: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:29"}
{"level":"warn","message":"⚠️ Shoukaku node europe closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:29"}
{"level":"warn","message":"⚠️ Shoukaku node europe closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:29"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node asia: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:29"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node asia: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:29"}
{"level":"warn","message":"⚠️ Shoukaku node asia closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:29"}
{"level":"warn","message":"⚠️ Shoukaku node asia closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:29"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:32"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:32"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node backup: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:32"}
{"level":"warn","message":"⚠️ Shoukaku node backup closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:32"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node asia: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:35"}
{"level":"warn","message":"⚠️ Shoukaku node asia closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node europe: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:46"}
{"level":"warn","message":"⚠️ Shoukaku node europe closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:46"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node backup: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node backup: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:46"}
{"level":"warn","message":"⚠️ Shoukaku node backup closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"level":"warn","message":"⚠️ Shoukaku node backup closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node europe: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node europe: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:46"}
{"level":"warn","message":"⚠️ Shoukaku node europe closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"level":"warn","message":"⚠️ Shoukaku node europe closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node asia: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node asia: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:46"}
{"level":"warn","message":"⚠️ Shoukaku node asia closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"level":"warn","message":"⚠️ Shoukaku node asia closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:46"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:48"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:48"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node backup: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:48"}
{"level":"warn","message":"⚠️ Shoukaku node backup closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:48"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node asia: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:53"}
{"level":"warn","message":"⚠️ Shoukaku node asia closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:53"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node europe: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:53"}
{"level":"warn","message":"⚠️ Shoukaku node europe closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:53"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node backup: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:56"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node backup: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:56"}
{"level":"warn","message":"⚠️ Shoukaku node backup closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:56"}
{"level":"warn","message":"⚠️ Shoukaku node backup closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:56"}
{"level":"warn","message":"⚠️ Shoukaku node backup disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:56"}
{"level":"warn","message":"⚠️ Shoukaku node backup disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:56"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:56"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:56"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:56"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:56"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:56"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:56"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node europe: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:59"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node europe: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:59"}
{"level":"warn","message":"⚠️ Shoukaku node europe closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:59"}
{"level":"warn","message":"⚠️ Shoukaku node europe closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:59"}
{"level":"warn","message":"⚠️ Shoukaku node europe disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:59"}
{"level":"warn","message":"⚠️ Shoukaku node europe disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:59"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node asia: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:59"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node asia: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:49:59"}
{"level":"warn","message":"⚠️ Shoukaku node asia closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:59"}
{"level":"warn","message":"⚠️ Shoukaku node asia closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:59"}
{"level":"warn","message":"⚠️ Shoukaku node asia disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:59"}
{"level":"warn","message":"⚠️ Shoukaku node asia disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:49:59"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node primary: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:50:01"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:50:01"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:50:01"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node backup: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:50:01"}
{"level":"warn","message":"⚠️ Shoukaku node backup closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:50:01"}
{"level":"warn","message":"⚠️ Shoukaku node backup disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:50:01"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node europe: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:50:04"}
{"level":"warn","message":"⚠️ Shoukaku node europe closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:50:04"}
{"level":"warn","message":"⚠️ Shoukaku node europe disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:50:04"}
{"error":{"code":"ENOTFOUND","errno":-3008,"hostname":"http","syscall":"getaddrinfo"},"level":"error","message":"❌ Shoukaku error on node asia: getaddrinfo ENOTFOUND http","service":"ultra-premium-music-bot","stack":"Error: getaddrinfo ENOTFOUND http\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-09-13 01:50:04"}
{"level":"warn","message":"⚠️ Shoukaku node asia closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:50:04"}
{"level":"warn","message":"⚠️ Shoukaku node asia disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:50:04"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:58:55"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 00:59:14 GMT","request-id":"29ab3fae-acf7-44ed-a8e3-75b9ab621907","server":"envoy","set-cookie":["__Host-device_id=AQAxZ6ZOaEiTNwOkqKh3RGopRKZknp1KyVDEqm0lKCL9pms2qhWNfy89rUoeEPuYrNoeXpLrXGeVIh-dXhS7Mvk075zxgvJnInI;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:25"}
{"level":"info","message":"✅ Bot successfully initialized and logged in!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:26"}
{"level":"info","message":"✅ Logged in as new music bot#4798!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:26"}
{"level":"info","message":"🎯 Bot is in 1 guild(s)","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:26"}
{"level":"info","message":"✅ 🎉 Bot is ready to serve!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:26"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"primary"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('primary')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('primary')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 01:59:26"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 01:59:26"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:26"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 01:59:26"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:26"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:26"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:26"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 01:59:26"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 01:59:26"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:00:37"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 01:00:56 GMT","request-id":"20bf31f9-e9a4-4423-bff8-daed8f11e3cc","server":"envoy","set-cookie":["__Host-device_id=AQBmQ3CAxuH7SCVyVZDn7J7ktlQ4EpzvYkACn_biXsQBW0Cxtu7fETqGFXe7wa9RbWauQ32AKAIFMqauxykpKxtcz2Vl8OsXk-s;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"7"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ Registered 0 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:07"}
{"level":"info","message":"✅ Bot successfully initialized and logged in!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:08"}
{"level":"info","message":"✅ Logged in as new music bot#4798!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:08"}
{"level":"info","message":"🎯 Bot is in 1 guild(s)","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:08"}
{"level":"info","message":"✅ 🎉 Bot is ready to serve!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:08"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"primary"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('primary')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('primary')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 02:01:08"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 02:01:08"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:08"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 02:01:08"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:08"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:08"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:08"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 02:01:08"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:08"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 02:01:13"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 02:01:13"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:13"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:13"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 02:01:13"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:13"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 02:01:18"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 02:01:18"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:18"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:18"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:18"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:18"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 02:01:18"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:18"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:01:18"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 02:02:23"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:05"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:05"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:05"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:05"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:05"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:05"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:05"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:06"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:06"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:06"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:06"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"warn","message":"⚠️ Lavalink connection timeout","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 21:02:24 GMT","request-id":"ea4df19b-41e2-4216-aa3e-08f4ac7acb0c","server":"envoy","set-cookie":["__Host-device_id=AQB-46hlzAFCyJwmdL1my0xCA0wp2S26VOPKZQ6w0gZlQ-83QEU1hreBiasEkVTZWyJIc6LHhYaGfdF3r1K7nrm9aNyWbZRkC0w;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ Registered 6 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:36"}
{"level":"info","message":"✅ Bot successfully initialized and logged in!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:37"}
{"level":"info","message":"✅ Logged in as new music bot#4798!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:37"}
{"level":"info","message":"🎯 Bot is in 1 guild(s)","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:37"}
{"level":"info","message":"✅ 🎉 Bot is ready to serve!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:37"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"primary"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('primary')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('primary')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 22:02:37"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:02:37"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:37"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:02:37"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:37"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:37"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:37"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:02:37"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:37"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:02:42"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:02:42"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:42"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:42"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:02:42"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:42"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:02:47"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:02:47"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:47"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:47"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:47"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:47"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:02:47"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:47"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:02:47"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:47"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:47"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:47"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:47"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:47"}
{"level":"info","message":"🚀 Starting Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:47"}
{"level":"info","message":"📝 Deploying commands...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:47"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:48"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:48"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:48"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:48"}
{"level":"info","message":"✅ Commands deployed successfully!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:48"}
{"level":"info","message":"🤖 Starting bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:48"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"primary"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('primary')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('primary')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 22:53:50"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:53:50"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:53:50"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 21:53:39 GMT","request-id":"4a11dd9f-5573-42bf-a669-141726380c7e","server":"envoy","set-cookie":["__Host-device_id=AQAbSTCw-kkZXgNe0p2BcJFg6I8zc1U-0mljUM6mloRfEQFyhsHV3iBsjquRIoD_HQAxiekZ-Q9KsKo_QFE2ViTHqJy5YD6MWM4;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"7"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:50"}
{"level":"info","message":"✅ Registered 6 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:51"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:51"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:51"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:51"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:51"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:53:55"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:53:55"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:55"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:55"}
{"level":"info","message":"✅ Bot successfully initialized and logged in!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:53:55"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:54:00"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:54:00"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:54:00"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:54:00"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:54:00"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:54:00"}
{"error":{},"level":"error","message":"❌ Error in play command: interaction.options.getString is not a function","service":"ultra-premium-music-bot","stack":"TypeError: interaction.options.getString is not a function\n    at Object.execute (C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\play.js:67:47)\n    at Object.execute (C:\\Users\\<USER>\\Documents\\new music boot\\src\\events\\discord\\interactionCreate.js:12:25)\n    at UltraPremiumMusicBot.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\src\\handlers\\eventHandler.js:45:60)\n    at UltraPremiumMusicBot.emit (node:events:524:28)\n    at InteractionCreateAction.handle (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord.js\\src\\client\\actions\\InteractionCreate.js:101:12)\n    at module.exports [as INTERACTION_CREATE] (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord.js\\src\\client\\websocket\\handlers\\INTERACTION_CREATE.js:4:36)\n    at WebSocketManager.handlePacket (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:352:31)\n    at WebSocketManager.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:236:12)\n    at WebSocketManager.emit (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)\n    at WebSocketShard.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\ws\\dist\\index.js:1190:51)","timestamp":"2025-09-13 22:54:39"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:54:39"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"🚀 Starting Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"📝 Deploying commands...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:54"}
{"level":"info","message":"✅ Commands deployed successfully!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:55"}
{"level":"info","message":"🤖 Starting bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:55"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"primary"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('primary')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('primary')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 22:57:55"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:57:55"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:55"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:57:55"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:55"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:55"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:55"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:55"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:55"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:55"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 21:57:44 GMT","request-id":"d9aa2147-362b-4db9-9511-c43470323f03","server":"envoy","set-cookie":["__Host-device_id=AQCLc_Yf2q5EeWEMIxVzWHfhOUxpxTAUPMsNx5gzmAgJCCy2itzue88FB5qnvjMVqdaJmSg_0KNYYny-b0uwKi1AHvOKAiT-wH0;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ Registered 6 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:57:56"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:58:00"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:58:01"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:58:01"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:58:01"}
{"level":"info","message":"✅ Bot successfully initialized and logged in!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:58:01"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:58:06"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 22:58:06"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:58:06"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:58:06"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:58:06"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:58:06"}
{"command":"play","guild":"op","guildId":"896509994541928469","level":"info","message":"⌨️ ⌨️ Command Executed: /play by .z2r in op","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:58:11","user":".z2r","userId":"770414138961952809"}
{"error":{},"level":"error","message":"❌ Error in play command: musicServiceInstance.searchTracks is not a function","service":"ultra-premium-music-bot","stack":"TypeError: musicServiceInstance.searchTracks is not a function\n    at searchTracks (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\musicService.js:989:39)\n    at Object.execute (C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\play.js:108:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-09-13 22:58:11"}
{"command":"play","error":"musicServiceInstance.searchTracks is not a function","guild":"op","guildId":"896509994541928469","level":"info","message":"⌨️ ❌ Command Error: /play by .z2r in op","service":"ultra-premium-music-bot","timestamp":"2025-09-13 22:58:11","user":".z2r","userId":"770414138961952809"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"🚀 Starting Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"📝 Deploying commands...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"🔌 Connecting to 1 Lavalink nodes...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:17"}
{"level":"info","message":"✅ Commands deployed successfully!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:18"}
{"level":"info","message":"🤖 Starting bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:18"}
{"error":{"code":"ERR_UNHANDLED_ERROR","context":"primary"},"level":"error","message":"❌ Uncaught Exception: Unhandled error. ('primary')","service":"ultra-premium-music-bot","stack":"Error [ERR_UNHANDLED_ERROR]: Unhandled error. ('primary')\n    at Shoukaku.emit (node:events:513:17)\n    at Node.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1283:40)\n    at Node.emit (node:events:524:28)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\shoukaku\\dist\\index.js:1049:41)\n    at WebSocket.emit (node:events:524:28)\n    at emitErrorAndClose (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:1041:13)\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\ws\\lib\\websocket.js:881:5)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:512:5)","timestamp":"2025-09-13 23:00:19"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 23:00:19"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 23:00:19"}
{"level":"warn","message":"⚠️ Lavalink connection error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"warn","message":"⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":1,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ NodeManager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Node manager initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 22:00:08 GMT","request-id":"fa2c424b-4a2d-4217-b262-8af784283d94","server":"envoy","set-cookie":["__Host-device_id=AQAHXFTuMdEcpv7ca_ZF3HVp_Oy0adlkx9Xp69yvJqeUsHObILWOPavmELiB6_w4dtwTjxb7f5mM_7pYaORzL1uZSqGIC9m6igw;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ Registered 6 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:19"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 23:00:24"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 23:00:24"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:24"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:24"}
{"level":"info","message":"✅ Bot successfully initialized and logged in!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:24"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 23:00:29"}
{"error":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":2333,"syscall":"connect"},"level":"error","message":"❌ Shoukaku error on node primary: connect ECONNREFUSED 127.0.0.1:2333","service":"ultra-premium-music-bot","stack":"Error: connect ECONNREFUSED 127.0.0.1:2333\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","timestamp":"2025-09-13 23:00:29"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:29"}
{"level":"warn","message":"⚠️ Shoukaku node primary closed: 1006 - ","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:29"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:29"}
{"level":"warn","message":"⚠️ Shoukaku node primary disconnected, 0 players affected, undefined moved","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:00:29"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🔇 Lavalink disabled - using Discord Player only","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🔇 Lavalink disabled - using Discord Player only","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🚀 Starting Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"📝 Deploying commands...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Cannot read properties of null (reading 'on')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of null (reading 'on')\n    at AudioService.setupEventHandlers (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:191:23)\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:108:18)","timestamp":"2025-09-13 23:03:17"}
{"error":{},"level":"error","message":"❌ ❌ Error initializing audio system: Cannot read properties of null (reading 'on')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of null (reading 'on')\n    at AudioService.setupEventHandlers (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:191:23)\n    at AudioService.initializeAudioSystem (C:\\Users\\<USER>\\Documents\\new music boot\\src\\services\\audioService.js:108:18)\n    at async UltraPremiumMusicBot.initializeServices (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:132:13)\n    at async UltraPremiumMusicBot.initializeBot (C:\\Users\\<USER>\\Documents\\new music boot\\src\\index.js:79:13)","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🔇 NodeManager skipped - Lavalink disabled","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 22:03:06 GMT","request-id":"7378faa6-7b76-483a-a18e-3e060a20a651","server":"envoy","set-cookie":["__Host-device_id=AQAeOoftYFnEr1AfDr3SHvr_LYZmLZF3URgJu_lQTMw1QtoXldxF4I4HGA1-wRTUkyK6gW1bWj0-IerC-dNkp9wn-3_O_3NOHhw;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"5"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ Registered 6 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ Commands deployed successfully!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"🤖 Starting bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:17"}
{"level":"info","message":"✅ Bot successfully initialized and logged in!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:24"}
{"level":"info","message":"✅ Logged in as new music bot#4798!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:24"}
{"level":"info","message":"🎯 Bot is in 1 guild(s)","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:24"}
{"level":"info","message":"✅ 🎉 Bot is ready to serve!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:03:24"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🔇 Lavalink disabled - using Discord Player only","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🔇 Lavalink disabled - using Discord Player only","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🚀 Starting Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"📝 Deploying commands...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"warn","message":"⚠️ ⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":0,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"warn","message":"⚠️ ⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":0,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🔇 NodeManager skipped - Lavalink disabled","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 22:04:36 GMT","request-id":"d2e1c56a-aa5e-4535-837b-b9f8827f3ed4","server":"envoy","set-cookie":["__Host-device_id=AQB6fdrRn_pfB_jCtXEN2FB1FwPLM_rxDZvz3Sp7_YC_z5FOiQUrHp-yKJQnBg8Xe2-e-1XDTORdttfoeqORwTNIjdd49PfaQ6M;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"6"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ Registered 6 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:47"}
{"level":"info","message":"✅ Commands deployed successfully!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:48"}
{"level":"info","message":"🤖 Starting bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:48"}
{"level":"info","message":"✅ Bot successfully initialized and logged in!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:53"}
{"level":"info","message":"✅ Logged in as new music bot#4798!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:53"}
{"level":"info","message":"🎯 Bot is in 1 guild(s)","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:53"}
{"level":"info","message":"✅ 🎉 Bot is ready to serve!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:04:53"}
{"command":"play","guild":"op","guildId":"896509994541928469","level":"info","message":"⌨️ ⌨️ Command Executed: /play by .z2r in op","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:05:00","user":".z2r","userId":"770414138961952809"}
{"error":{},"level":"error","message":"❌ Error creating queue: Cannot read properties of undefined (reading 'nodes')","service":"ultra-premium-music-bot","stack":"TypeError: Cannot read properties of undefined (reading 'nodes')\n    at Object.execute (C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\play.js:137:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-09-13 23:05:01"}
{"level":"info","message":"🚀 Initializing Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"info","message":"🔧 Initializing core services...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"info","message":"🔇 Lavalink disabled - using Discord Player only","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"info","message":"🔇 Lavalink disabled - using Discord Player only","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"info","message":"🚀 Starting Ultra-Premium Discord Music Bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"info","message":"📝 Deploying commands...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:57"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Default audio extractors registered","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"warn","message":"⚠️ ⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":0,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"warn","message":"⚠️ ⚠️ Proceeding without Lavalink connection","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Audio system initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"extractors":"Spotify, YouTube, SoundCloud, Apple Music, Deezer","lavasfy":"Disabled","level":"info","message":"🎚️ Audio system initialized","nodes":0,"service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Audio service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"🔇 NodeManager skipped - Lavalink disabled","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"🎵 Initializing music sources...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Music service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"🗄️ Initializing cache service...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Cache service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"🎛️ Initializing audio enhancements...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Audio enhancements initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"🎼 Initializing audio presets...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Audio presets initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"🤖 Initializing AI audio models...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ AI audio models initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Audio enhancement service initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ Loaded 6 commands","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"error":{"body":{"error":"invalid_client","error_description":"Invalid client"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000, h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","connection":"close","content-encoding":"gzip","content-type":"application/json","date":"Sat, 13 Sep 2025 22:06:46 GMT","request-id":"31dcc625-5914-42bd-b27a-3f41c2cdb7d5","server":"envoy","set-cookie":["__Host-device_id=AQCMfWdmGPwoLgWH_e3Iey9nbINdqwdAA3H_p1bkA3JF8AxB5auZv0Ih7Jy4X-R6Yd7glE8v6yrsAa6GvpZkyOQjlvk9TzVazo0;Version=1;Path=/;Max-Age=**********;Secure;HttpOnly;SameSite=Lax","sp_tr=false;Version=1;Domain=accounts.spotify.com;Path=/;Secure;SameSite=Lax"],"strict-transport-security":"max-age=********","transfer-encoding":"chunked","vary":"Accept-Encoding","via":"HTTP/2 edgeproxy, 1.1 google","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"7"},"statusCode":400},"level":"error","message":"❌ ❌ Error initializing Spotify API: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.","service":"ultra-premium-music-bot","stack":"WebapiAuthenticationError: An authentication error occurred while communicating with Spotify's Web API.\nDetails: invalid_client Invalid client.\n    at _toError (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:43:12)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\spotify-web-api-node\\src\\http-manager.js:71:25\n    at Request.callback (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:905:3)\n    at C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\index.js:1127:20\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\parsers\\json.js:22:7)\n    at Stream.emit (node:events:524:28)\n    at Unzip.<anonymous> (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\superagent\\lib\\node\\unzip.js:53:12)\n    at Unzip.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ YouTube extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ SoundCloud extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"warn","message":"⚠️ ⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"warn","message":"⚠️ ⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Bandcamp extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Radio extractor initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"warn","message":"⚠️ Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ All music sources initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ Registered 6 commands for guild 896509994541928469","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Command handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ Loaded events from 1 categories","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ ✅ Event handler initialized","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ 🎉 All services initialized successfully","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ Commands deployed successfully!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"🤖 Starting bot...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:58"}
{"level":"info","message":"✅ Logged in as new music bot#4798!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:59"}
{"level":"info","message":"🎯 Bot is in 1 guild(s)","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:59"}
{"level":"info","message":"✅ 🎉 Bot is ready to serve!","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:06:59"}
{"command":"play","guild":"op","guildId":"896509994541928469","level":"info","message":"⌨️ ⌨️ Command Executed: /play by .z2r in op","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","user":".z2r","userId":"770414138961952809"}
{"artist":"Sy Mehdi | سي مهدي","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: Sy Mehdi - Finito (Official Music Video) | سي مهدي - فينيتو by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"Sy Mehdi - Finito (Official Music Video) | سي مهدي - فينيتو","user":".z2r","userId":"770414138961952809"}
{"artist":"Legendes Industries","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: FINITO LE RAP, EDGE FORCES US TO MAKE R&B 2000 by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"FINITO LE RAP, EDGE FORCES US TO MAKE R&B 2000","user":".z2r","userId":"770414138961952809"}
{"artist":"is jbara","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: NINJA x LOUKMAN - FINITO ( PROD BY AK96 ) by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"NINJA x LOUKMAN - FINITO ( PROD BY AK96 )","user":".z2r","userId":"770414138961952809"}
{"artist":"Gapman","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: Gapman - Finito (Official Video) by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"Gapman - Finito (Official Video)","user":".z2r","userId":"770414138961952809"}
{"artist":"officialiyanya","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: Iyanya - Finito [Official Video] by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"Iyanya - Finito [Official Video]","user":".z2r","userId":"770414138961952809"}
{"artist":"RAF Camora","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: Finito by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"Finito","user":".z2r","userId":"770414138961952809"}
{"artist":"BZ Fabri","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: Thirst - FINITO by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"Thirst - FINITO","user":".z2r","userId":"770414138961952809"}
{"artist":"WORLDSTARHIPHOP","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: Chief Keef \"Faneto\" (WSHH Exclusive - Official Music Video) by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"Chief Keef \"Faneto\" (WSHH Exclusive - Official Music Video)","user":".z2r","userId":"770414138961952809"}
{"artist":"DailyBuzz","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: Sy Mehdi, ACH GALOU 3LIK ? (Finito) l DailyBuzz by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"Sy Mehdi, ACH GALOU 3LIK ? (Finito) l DailyBuzz","user":".z2r","userId":"770414138961952809"}
{"artist":"BOXEO A LO KBRON","guild":"op","guildId":"896509994541928469","level":"info","message":"🎵 ➕ Added to queue: 😱“¡BOMBAZO DEL FINITO!”💥 Su pronóstico para Canelo vs Crawford ENCENDIÓ las redes🔥 by .z2r","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","track":"😱“¡BOMBAZO DEL FINITO!”💥 Su pronóstico para Canelo vs Crawford ENCENDIÓ las redes🔥","user":".z2r","userId":"770414138961952809"}
{"level":"error","message":"❌ Unhandled Rejection at:","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05"}
{"error":{"code":50035,"method":"PATCH","rawError":{"code":50035,"errors":{"embeds":{"0":{"description":{"_errors":[{"code":"BASE_TYPE_REQUIRED","message":"This field is required"}]}}}},"message":"Invalid Form Body"},"requestBody":{"files":[],"json":{"embeds":[{}],"enforce_nonce":false,"tts":false}},"status":400,"url":"https://discord.com/api/v10/webhooks/1414830939929972746/aW50ZXJhY3Rpb246MTQxNjU0NTcwMTM5MDcxMjkwMjpIUzVxVnJGMmVJaWsyZmxnMVFCSDVWN21RZVp1cEJubGpBVUM5elh0b1Z2a3Q5UDhXVWljcTJkSk9JQjNESXVDNE4zRTZ5QTh3ZW90d2k5SWphRllBZTlXN2tHY0wzQ1lKUHFKRUVFeGZ5Nm43ZU1RNG1LYjJHT29malVYR2Yxdw/messages/%40original"},"level":"error","message":"❌ Error in play command: Invalid Form Body\nembeds[0].description[BASE_TYPE_REQUIRED]: This field is required","service":"ultra-premium-music-bot","stack":"DiscordAPIError[50035]: Invalid Form Body\nembeds[0].description[BASE_TYPE_REQUIRED]: This field is required\n    at handleErrors (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\rest\\dist\\index.js:762:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SequentialHandler.runRequest (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\rest\\dist\\index.js:1163:23)\n    at async SequentialHandler.queueRequest (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\rest\\dist\\index.js:994:14)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\@discordjs\\rest\\dist\\index.js:1307:22)\n    at async InteractionWebhook.editMessage (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord.js\\src\\structures\\Webhook.js:351:15)\n    at async ChatInputCommandInteraction.editReply (C:\\Users\\<USER>\\Documents\\new music boot\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:254:17)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\new music boot\\src\\commands\\music\\play.js:226:17)","timestamp":"2025-09-13 23:07:05"}
{"command":"play","error":"Invalid Form Body\nembeds[0].description[BASE_TYPE_REQUIRED]: This field is required","guild":"op","guildId":"896509994541928469","level":"info","message":"⌨️ ❌ Command Error: /play by .z2r in op","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:05","user":".z2r","userId":"770414138961952809"}
{"level":"info","message":"🔄 Shutting down gracefully...","service":"ultra-premium-music-bot","timestamp":"2025-09-13 23:07:27"}
