{"version": 3, "file": "Music.js", "sourceRoot": "", "sources": ["../../../../src/core/clients/Music.ts"], "names": [], "mappings": ";;AAAA,OAAO,KAAK,KAAK,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,cAAc,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAE5F,OAAO,EACL,KAAK,EAAE,MAAM,EAAE,OAAO,EACtB,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAC3B,KAAK,EAAE,MAAM,EAAE,SAAS,EACzB,MAAM,+BAA+B,CAAC;AAEvC,OAAO,mBAAmB,MAAM,6CAA6C,CAAC;AAC9E,OAAO,OAAO,MAAM,iCAAiC,CAAC;AACtD,OAAO,qBAAqB,MAAM,+CAA+C,CAAC;AAClF,OAAO,UAAU,MAAM,oCAAoC,CAAC;AAC5D,OAAO,eAAe,MAAM,yCAAyC,CAAC;AACtE,OAAO,aAAa,MAAM,uCAAuC,CAAC;AAClE,OAAO,wBAAwB,MAAM,kDAAkD,CAAC;AACxF,OAAO,WAAW,MAAM,qCAAqC,CAAC;AAC9D,OAAO,GAAG,MAAM,6BAA6B,CAAC;AAE9C,OAAO,EACL,cAAc,EACd,YAAY,EACZ,cAAc,EACd,cAAc,EACf,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,4BAA4B,EAAE,MAAM,6BAA6B,CAAC;AAM3E,MAAqB,KAAK;IAIxB,YAAY,OAAgB;;QAH5B,iCAAkB;QAClB,iCAAkB;QAGhB,uBAAA,IAAI,kBAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,kBAAY,OAAO,CAAC,OAAO,MAAA,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,MAAgC;QACtC,IAAI,MAAM,YAAY,eAAe,EAAE;YACrC,OAAO,uBAAA,IAAI,sDAAuB,MAA3B,IAAI,EAAwB,MAAM,CAAC,CAAC;SAC5C;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YACrC,OAAO,uBAAA,IAAI,qDAAsB,MAA1B,IAAI,EAAuB,MAAM,CAAC,CAAC;SAC3C;QAED,MAAM,IAAI,cAAc,CAAC,uEAAuE,EAAE,MAAM,CAAC,CAAC;IAC5G,CAAC;IAqDD;;;;OAIG;IACG,MAAM,CAAC,KAAa,EAAE,UAA8B,EAAE;;YAC1D,cAAc,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAE1B,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,KAAK,EAAE,MAAM,EAAE,SAAS;gBACxB,MAAM,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;aACrG,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,uBAAA,IAAI,sBAAS,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;QACrG,CAAC;KAAA;IAED;;OAEG;IACG,WAAW;;YACf,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,SAAS,EAAE,cAAc;gBACzB,MAAM,EAAE,SAAS;aAClB,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,QAAQ,CAAC,QAAQ,EAAE,uBAAA,IAAI,sBAAS,CAAC,CAAC;QAC/C,CAAC;KAAA;IAED;;OAEG;IACG,UAAU;;YACd,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,iBAAiB;aAC7B,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC7B,qDAAqD;QACvD,CAAC;KAAA;IAED;;OAEG;IACG,UAAU;;YACd,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,yBAAyB;aACrC,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,uBAAA,IAAI,sBAAS,CAAC,CAAC;QAC9C,CAAC;KAAA;IAED;;;OAGG;IACG,SAAS,CAAC,SAAiB;;YAC/B,cAAc,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;YAE9B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,wCAAwC,CAAC;gBAChG,MAAM,IAAI,cAAc,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;YAE3D,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,SAAS;aACrB,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,uBAAA,IAAI,sBAAS,CAAC,CAAC;QAC7C,CAAC;KAAA;IAED;;;OAGG;IACG,QAAQ,CAAC,QAAgB;;YAC7B,cAAc,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE7B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,yCAAyC,CAAC;gBAChG,MAAM,IAAI,cAAc,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAEzD,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,QAAQ;aACpB,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;KAAA;IAED;;;OAGG;IACG,WAAW,CAAC,WAAmB;;YACnC,cAAc,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;YAEhC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBACjC,WAAW,GAAG,KAAK,WAAW,EAAE,CAAC;aAClC;YAED,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,WAAW;aACvB,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,QAAQ,CAAC,QAAQ,EAAE,uBAAA,IAAI,sBAAS,CAAC,CAAC;QAC/C,CAAC;KAAA;IAED;;;;OAIG;IACG,SAAS,CAAC,QAAgB,EAAE,OAAO,GAAG,IAAI;;;YAC9C,cAAc,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE7B,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,YAAY,CAAC,IAAI,kCAAO,YAAY,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,KAAE,KAAK,EAAE,IAAI,IACzF,CAAC;YAEF,MAAM,IAAI,GAAG,MAAA,QAAQ,CAAC,aAAa,0CAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YAElD,MAAM,GAAG,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,EAAE,CAAC;YAE1B,IAAI,CAAC,GAAG;gBACN,MAAM,IAAI,cAAc,CAAC,4BAA4B,CAAC,CAAC;YAEzD,MAAM,WAAW,GAAG,MAAA,GAAG,CAAC,OAAO,0CAAE,EAAE,CAAC,UAAU,CAAC,CAAC;YAEhD,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,OAAO;gBACtC,MAAM,IAAI,cAAc,CAAC,0DAA0D,EAAE,WAAW,CAAC,CAAC;YAEpG,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;YAE7D,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,OAAO,EAAE;gBAC1C,MAAM,qBAAqB,GAAG,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;gBAEvF,IAAI,CAAC,qBAAqB;oBACxB,MAAM,IAAI,cAAc,CAAC,wBAAwB,CAAC,CAAC;gBAErD,MAAM,IAAI,GAAG,MAAM,CAAA,MAAA,qBAAqB,CAAC,cAAc,0CAAE,QAAQ,CAAC,IAAI,CAAC,uBAAA,IAAI,sBAAS,EAAE;oBACpF,OAAO,EAAE,QAAQ;oBACjB,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAA,CAAC;gBAEH,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa;oBAC9B,MAAM,IAAI,cAAc,CAAC,yBAAyB,CAAC,CAAC;gBAEtD,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,KAAK,EAAE,CAAC;aAC1D;YAED,OAAO,cAAc,CAAC;;KACvB;IAED;;;OAGG;IACG,UAAU,CAAC,QAAgB;;;YAC/B,cAAc,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE7B,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,YAAY,CAAC,IAAI,kCAAO,YAAY,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,KAAE,KAAK,EAAE,IAAI,IACzF,CAAC;YAEF,MAAM,IAAI,GAAG,MAAA,QAAQ,CAAC,aAAa,0CAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YAElD,MAAM,GAAG,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,cAAc,CAAC,CAAC,GAAG,EAAE,EAAE,eAAC,OAAA,CAAA,MAAA,MAAA,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,qCAAqC,0CAAE,gCAAgC,0CAAE,QAAQ,MAAK,+BAA+B,CAAA,EAAA,CAAC,CAAC;YAEtL,IAAI,CAAC,GAAG;gBACN,MAAM,IAAI,cAAc,CAAC,4BAA4B,CAAC,CAAC;YAEzD,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAA,IAAI,sBAAS,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAExF,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAChB,MAAM,IAAI,cAAc,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;YAExD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAE/D,OAAO,QAAQ,CAAC;;KACjB;IAED;;;OAGG;IACG,SAAS,CAAC,QAAgB;;;YAC9B,cAAc,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE7B,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,YAAY,CAAC,IAAI,kCAAO,YAAY,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,KAAE,KAAK,EAAE,IAAI,IACzF,CAAC;YAEF,MAAM,IAAI,GAAG,MAAA,QAAQ,CAAC,aAAa,0CAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YAElD,MAAM,GAAG,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,cAAc,CAAC,CAAC,GAAG,EAAE,EAAE,eAAC,OAAA,CAAA,MAAA,MAAA,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,qCAAqC,0CAAE,gCAAgC,0CAAE,QAAQ,MAAK,8BAA8B,CAAA,EAAA,CAAC,CAAC;YAErL,IAAI,CAAC,GAAG;gBACN,MAAM,IAAI,cAAc,CAAC,4BAA4B,CAAC,CAAC;YAEzD,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAA,IAAI,sBAAS,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAExF,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAChB,MAAM,IAAI,cAAc,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;YAExD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,SAAS;gBACzC,MAAM,IAAI,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;YAEvF,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC;YAEnE,OAAO,YAAY,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;;KACxD;IAED;;OAEG;IACG,QAAQ;;YACZ,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;gBACxC,MAAM,EAAE,iBAAiB;gBACzB,SAAS,EAAE,0BAA0B;aACtC,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE,uBAAA,IAAI,sBAAS,CAAC,CAAC;QAC5C,CAAC;KAAA;IAED;;;OAGG;IACG,oBAAoB,CAAC,KAAa;;YACtC,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAC1C,4BAA4B,CAAC,IAAI,kCAC5B,4BAA4B,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,KAAE,KAAK,EAAE,IAAI,IACvE,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,aAAa;gBACzB,OAAO,EAAwD,CAAC;YAElE,MAAM,2BAA2B,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAE7F,OAAO,2BAA2B,CAAC;QACrC,CAAC;KAAA;CACF;qKAtT6B,QAAgB;;;QAC1C,MAAM,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC;YAC1C,QAAQ;YACR,GAAG,EAAE,MAAA,uBAAA,IAAI,sBAAS,CAAC,MAAM,0CAAE,GAAG;YAC9B,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC;YACtC,QAAQ;YACR,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QACnF,MAAM,aAAa,GAAG,uBAAA,IAAI,sBAAS,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAC7E,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAE,eAAe,EAAE,aAAa,CAAE,CAAC,CAAC;QAEvE,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,CAAC,CAAC;QAErC,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,uBAAA,IAAI,sBAAS,EAAE,GAAG,CAAC,CAAC;;wEAGxB,SAAsC;;;QACjE,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,cAAc,CAAC,+BAA+B,CAAC,CAAC;QAE5D,IAAI,CAAC,SAAS,CAAC,QAAQ;YACrB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAE1D,MAAM,eAAe,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAA,IAAI,sBAAS,EAAE;YAC7D,MAAM,EAAE,SAAS;YACjB,eAAe,EAAE;gBACf,sBAAsB,gBACjB;oBACD,kBAAkB,EAAE,MAAA,uBAAA,IAAI,sBAAS,CAAC,MAAM,0CAAE,GAAG;iBAC9C,CACF;aACF;SACF,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAA,IAAI,sBAAS,EAAE;YAC3D,MAAM,EAAE,SAAS;YACjB,6BAA6B,EAAE,IAAI;YACnC,iBAAiB,EAAE,OAAO;SAC3B,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,CAAC,CAAC;QAErC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAE,eAAe,EAAE,aAAa,CAAE,CAAC,CAAC;QACvE,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,uBAAA,IAAI,sBAAS,EAAE,GAAG,CAAC,CAAC;;;eAvElC,KAAK"}