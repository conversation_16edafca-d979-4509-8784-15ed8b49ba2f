require('dotenv').config();

module.exports = {
    // Discord Bot Configuration
    token: process.env.DISCORD_TOKEN || 'YOUR_DISCORD_BOT_TOKEN',
    clientId: process.env.CLIENT_ID || 'YOUR_CLIENT_ID',
    guildId: process.env.GUILD_ID || 'YOUR_GUILD_ID',
    
    // Lavalink Configuration (DISABLED)
    lavalink: {
        enabled: false, // Disabled temporarily
        nodes: [
            // {
            //     name: 'primary',
            //     // <PERSON><PERSON><PERSON><PERSON> expects host:port without protocol
            //     url: `${process.env.LAVALINK_HOST || 'localhost'}:${process.env.LAVALINK_PORT || 2333}`,
            //     auth: process.env.LAVALINK_PRIMARY_AUTH || process.env.LAVALINK_PASSWORD || 'youshallnotpass',
            //     region: 'us',
            //     secure: false
            // }
        ],
        settings: {
            autoPlay: true,
            defaultVolume: 80,
            maxVolume: 150,
            defaultSource: 'youtube',
            leaveOnEmpty: true,
            leaveOnEmptyCooldown: 300000, // 5 minutes
            leaveOnEnd: true,
            leaveOnEndCooldown: 300000, // 5 minutes
            leaveOnStop: true,
            leaveOnStopCooldown: 300000, // 5 minutes
            connectionTimeout: 30000,
            bufferingTimeout: 3000,
            spotifyBridge: true,
            appleBridge: true,
            deezerBridge: true,
            enableLive: true,
            enableDiscordEncodings: true,
            disableVolume: false,
            disableEqualizer: false,
            disableFilters: false,
            disableResampler: false,
            disableBiquad: false,
            disableFfmpeg: false
        }
    },

    // Music Source Configuration
    sources: {
        spotify: {
            clientId: process.env.SPOTIFY_CLIENT_ID || 'YOUR_SPOTIFY_CLIENT_ID',
            clientSecret: process.env.SPOTIFY_CLIENT_SECRET || 'YOUR_SPOTIFY_CLIENT_SECRET',
            market: 'US'
        },
        appleMusic: {
            developerToken: process.env.APPLE_MUSIC_TOKEN || 'YOUR_APPLE_MUSIC_TOKEN',
            storefront: 'us'
        },
        youtube: {
            apiKey: process.env.YOUTUBE_API_KEY || 'YOUR_YOUTUBE_API_KEY',
            cookie: process.env.YOUTUBE_COOKIE || '',
            useOAuth: false,
            oauthCredentials: {
                client_id: process.env.YOUTUBE_OAUTH_CLIENT_ID || '',
                client_secret: process.env.YOUTUBE_OAUTH_CLIENT_SECRET || '',
                redirect_uri: process.env.YOUTUBE_OAUTH_REDIRECT_URI || ''
            }
        },
        soundcloud: {
            clientId: process.env.SOUNDCLOUD_CLIENT_ID || 'YOUR_SOUNDCLOUD_CLIENT_ID',
            clientSecret: process.env.SOUNDCLOUD_CLIENT_SECRET || 'YOUR_SOUNDCLOUD_CLIENT_SECRET'
        },
        deezer: {
            appId: process.env.DEEZER_APP_ID || 'YOUR_DEEZER_APP_ID',
            secret: process.env.DEEZER_SECRET || 'YOUR_DEEZER_SECRET'
        },
        tidal: {
            clientId: process.env.TIDAL_CLIENT_ID || 'YOUR_TIDAL_CLIENT_ID',
            clientSecret: process.env.TIDAL_CLIENT_SECRET || 'YOUR_TIDAL_CLIENT_SECRET'
        }
    },

    // AI Services Configuration
    ai: {
        openai: {
            apiKey: process.env.OPENAI_API_KEY || 'YOUR_OPENAI_API_KEY',
            model: 'gpt-4',
            maxTokens: 1000,
            temperature: 0.7
        },
        googleTranslate: {
            apiKey: process.env.GOOGLE_TRANSLATE_API_KEY || 'YOUR_GOOGLE_TRANSLATE_API_KEY'
        },
        genius: {
            accessToken: process.env.GENIUS_ACCESS_TOKEN || 'YOUR_GENIUS_ACCESS_TOKEN'
        },
        musixmatch: {
            apiKey: process.env.MUSIXMATCH_API_KEY || 'YOUR_MUSIXMATCH_API_KEY'
        }
    },

    // Database Configuration
    database: {
        mongodb: {
            url: process.env.MONGODB_URL || 'mongodb://localhost:27017/ultra-premium-music-bot',
            options: {
                useNewUrlParser: true,
                useUnifiedTopology: true,
                maxPoolSize: 10,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000
            }
        },
        redis: {
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            password: process.env.REDIS_PASSWORD || '',
            db: parseInt(process.env.REDIS_DB) || 0,
            keyPrefix: 'upmb:',
            retryDelayOnFailover: 100,
            enableReadyCheck: true,
            maxRetriesPerRequest: 3
        }
    },

    // Security Configuration
    security: {
        encryptionKey: process.env.ENCRYPTION_KEY || 'YOUR_ENCRYPTION_KEY',
        jwtSecret: process.env.JWT_SECRET || 'YOUR_JWT_SECRET',
        bcryptRounds: 12,
        rateLimiting: {
            windowMs: 60000, // 1 minute
            max: 100, // limit each IP to 100 requests per windowMs
            message: 'Too many requests from this IP, please try again later.'
        },
        antiSpam: {
            maxMessages: 5,
            interval: 5000, // 5 seconds
            warningMessage: 'Please stop spamming!',
            kickMessage: 'You have been kicked for spamming.',
            banMessage: 'You have been banned for spamming.'
        }
    },

    // Premium Features Configuration
    premium: {
        maxQueueSize: 1000,
        maxFilters: 20,
        multiChannel: true,
        aiGeneration: true,
        advancedFilters: true,
        prioritySupport: true,
        customPresets: true,
        unlimitedSkips: true,
        highQualityAudio: true,
        lyricsTranslation: true,
        moodDetection: true,
        smartRecommendations: true
    },

    // Audio Processing Configuration
    audio: {
        sampleRate: 48000,
        channels: 2,
        bitrate: 192,
        opusEncoder: {
            application: 'audio',
            frameSize: 960,
            sampleRate: 48000,
            channels: 2,
            bitrate: 192
        },
        filters: {
            bassboost: {
                gain: 8
            },
            equalizer: {
                bands: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
            },
            nightcore: {
                rate: 1.25
            },
            vaporwave: {
                rate: 0.8
            },
            pop: {
                rate: 1.0
            },
            soft: {
                rate: 0.8
            },
            treblebass: {
                gain: 8
            },
            eightD: {
                rotation: 0.2
            },
            karaoke: {
                level: 1.0,
                monoLevel: 1.0,
                filterBand: 220.0,
                filterWidth: 100.0
            }
        }
    },

    // CDN and Performance Configuration
    performance: {
        cdn: {
            enabled: true,
            baseUrl: process.env.CDN_BASE_URL || 'https://cdn.yourdomain.com',
            apiKey: process.env.CDN_API_KEY || 'YOUR_CDN_API_KEY'
        },
        caching: {
            enabled: true,
            defaultTTL: 3600, // 1 hour
            trackCacheSize: 10000,
            playlistCacheSize: 1000,
            userCacheSize: 5000
        },
        clustering: {
            enabled: true,
            totalShards: 'auto',
            shardList: 'auto',
            respawn: true,
            timeout: 60000
        }
    },

    // Logging Configuration
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: {
            enabled: true,
            filename: 'bot.log',
            maxSize: '10m',
            maxFiles: 5
        },
        console: {
            enabled: true,
            colorize: true,
            timestamp: true
        }
    },

    // API Configuration
    api: {
        enabled: true,
        port: parseInt(process.env.API_PORT) || 3000,
        host: process.env.API_HOST || 'localhost',
        cors: {
            origin: process.env.API_CORS_ORIGIN || '*',
            credentials: true
        },
        rateLimit: {
            windowMs: 60000,
            max: 100
        }
    }
};
