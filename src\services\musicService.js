const { Player } = require('discord-player');
const { SpotifyExtractor } = require('@discord-player/extractor');
const { YouTubeExtractor } = require('@discord-player/extractor');
const { SoundCloudExtractor } = require('@discord-player/extractor');
const { AppleMusicExtractor } = require('@discord-player/extractor');
const { DeezerExtractor } = require('@discord-player/extractor');
const { AttachmentExtractor } = require('@discord-player/extractor');
const { Lavasfy } = require('lavasfy');
const playdl = require('play-dl');
const SpotifyWebApi = require('spotify-web-api-node');
const axios = require('axios');
const logger = require('../utils/logger');
const CacheService = require('../cache/cacheService');
const { ReverbnationExtractor } = require('@discord-player/extractor');
const { HttpExtractor } = require('@discord-player/extractor');
const { GenericExtractor } = require('@discord-player/extractor');

// Third-party extractors
const { default: Spotify } = require('erela.js-spotify');
const { default: AppleMusic } = require('erela.js-apple');
const { default: Deezer } = require('erela.js-deezer');

const spotifyApi = require('spotify-web-api-node');
// const appleMusic = require('apple-music');
// Optional third-party APIs will be required lazily in their initializers to avoid crashing when not installed
const ytdl = require('ytdl-core');
const soundcloud = require('soundcloud-downloader');

class MusicService {
    constructor(bot) {
        this.bot = bot;
        this.cache = new CacheService(bot);
        this.sources = new Map();
        this.extractors = new Map();
        this.apis = new Map();
        this.lavasfy = null;
        this.spotifyApi = null;
        this.sourceStats = {
            youtube: { searches: 0, tracks: 0, errors: 0 },
            spotify: { searches: 0, tracks: 0, errors: 0 },
            apple: { searches: 0, tracks: 0, errors: 0 },
            soundcloud: { searches: 0, tracks: 0, errors: 0 },
            deezer: { searches: 0, tracks: 0, errors: 0 },
            tidal: { searches: 0, tracks: 0, errors: 0 },
            bandcamp: { searches: 0, tracks: 0, errors: 0 },
            radio: { searches: 0, tracks: 0, errors: 0 }
        };
        
        this.initializeSources();
    }

    async initializeSources() {
        try {
            logger.info('🎵 Initializing music sources...');
            
            // Initialize Spotify
            await this.initializeSpotify();
            
            // Initialize Apple Music (Temporarily Disabled)
            // await this.initializeAppleMusic();
            
            // Initialize YouTube
            await this.initializeYouTube();
            
            // Initialize SoundCloud
            await this.initializeSoundCloud();
            
            // Initialize Deezer
            await this.initializeDeezer();
            
            // Initialize Tidal
            await this.initializeTidal();
            
            // Initialize Bandcamp
            await this.initializeBandcamp();
            
            // Initialize Radio
            await this.initializeRadio();
            
            // Initialize Lavasfy for Spotify support
            if (this.bot.config.sources.spotify.clientId && this.bot.config.sources.spotify.clientSecret) {
                try {
                    if (typeof Lavasfy === 'function') {
                        this.lavasfy = new Lavasfy({
                            clientID: this.bot.config.sources.spotify.clientId,
                            clientSecret: this.bot.config.sources.spotify.clientSecret,
                            playlistLoadLimit: 100,
                            albumLoadLimit: 50,
                            searchLimit: 20,
                            audioOnlyResults: true
                        }, this.bot.config.lavalink.nodes);
                    } else {
                        logger.warn('Lavasfy package loaded but constructor not found. Skipping Lavasfy integration.');
                    }
                } catch (e) {
                    logger.warn('Failed to initialize Lavasfy. Spotify URL playback via Lavalink will be disabled.');
                }
            }
            
            logger.success('✅ All music sources initialized');
            
        } catch (error) {
            logger.error('❌ Error initializing music sources:', error);
            throw error;
        }
    }

    async initializeSpotify() {
        try {
            if (this.bot.config.sources.spotify.clientId && this.bot.config.sources.spotify.clientSecret) {
                this.spotifyApi = new SpotifyWebApi({
                    clientId: this.bot.config.sources.spotify.clientId,
                    clientSecret: this.bot.config.sources.spotify.clientSecret
                });
                
                // Get access token
                const spotifyToken = await this.spotifyApi.clientCredentialsGrant();
                this.spotifyApi.setAccessToken(spotifyToken.body.access_token);
                
                logger.success('✅ Spotify API initialized');
            }
        } catch (error) {
            logger.error('❌ Error initializing Spotify API:', error);
        }
    }

    async initializeAppleMusic() {
        try {
            if (this.bot.config.sources.appleMusic.developerToken) {
                // this.apis.set('appleMusic', new appleMusic(this.bot.config.sources.appleMusic.developerToken));
                logger.warn('⚠️ Apple Music API disabled due to missing `apple-music` package.');
            }
        } catch (error) {
            logger.error('❌ Error initializing Apple Music API:', error);
        }
    }

    async initializeYouTube() {
        try {
            // Initialize YouTube extractor
            if (this.bot.audioService && this.bot.audioService.player) {
                await this.bot.audioService.player.extractors.register(YouTubeExtractor);
            }
            logger.success('✅ YouTube extractor initialized');
        } catch (error) {
            logger.error('❌ Error initializing YouTube extractor:', error);
        }
    }

    async initializeSoundCloud() {
        try {
            // Initialize SoundCloud extractor
            if (this.bot.audioService && this.bot.audioService.player) {
                await this.bot.audioService.player.extractors.register(SoundCloudExtractor);
            }
            logger.success('✅ SoundCloud extractor initialized');
        } catch (error) {
            logger.error('❌ Error initializing SoundCloud extractor:', error);
        }
    }

    async initializeDeezer() {
        try {
            if (this.bot.config.sources.deezer.appId && this.bot.config.sources.deezer.secret) {
                let DeezerPublicApi;
                try {
                    DeezerPublicApi = require('deezer-public-api');
                } catch (e) {
                    if (e && e.code === 'MODULE_NOT_FOUND') {
                        logger.warn('⚠️ deezer-public-api is not installed. Skipping Deezer API initialization.');
                        return;
                    }
                    throw e;
                }
                this.apis.set('deezer', new DeezerPublicApi());
                logger.success('✅ Deezer API initialized');
            }
        } catch (error) {
            logger.error('❌ Error initializing Deezer API:', error);
        }
    }

    async initializeTidal() {
        try {
            if (this.bot.config.sources.tidal.clientId && this.bot.config.sources.tidal.clientSecret) {
                let TidalApi;
                try {
                    TidalApi = require('tidal-api-wrapper');
                } catch (e) {
                    if (e && e.code === 'MODULE_NOT_FOUND') {
                        logger.warn('⚠️ tidal-api-wrapper is not installed. Skipping Tidal API initialization.');
                        return;
                    }
                    throw e;
                }
                this.apis.set('tidal', new TidalApi({
                    clientId: this.bot.config.sources.tidal.clientId,
                    clientSecret: this.bot.config.sources.tidal.clientSecret
                }));
                logger.success('✅ Tidal API initialized');
            }
        } catch (error) {
            logger.error('❌ Error initializing Tidal API:', error);
        }
    }

    async initializeBandcamp() {
        try {
            // Initialize Bandcamp extractor
            // Note: Bandcamp API implementation would go here
            // This is a placeholder implementation
            logger.success('✅ Bandcamp extractor initialized');
        } catch (error) {
            logger.error('❌ Error initializing Bandcamp extractor:', error);
        }
    }

    async initializeRadio() {
        try {
            // Initialize Radio extractor
            // Note: Radio API implementation would go here
            // This is a placeholder implementation
            logger.success('✅ Radio extractor initialized');
        } catch (error) {
            logger.error('❌ Error initializing Radio extractor:', error);
        }
    }

    async searchSpotify(query, options = {}) {
        try {
            if (!this.spotifyApi) {
                throw new Error('Spotify API not initialized');
            }
            
            const { limit = 10, type = 'track' } = options;
            
            // Check if query is a Spotify URL
            if (query.includes('open.spotify.com')) {
                return await this.getSpotifyTrack(query, options);
            }
            
            // Search Spotify
            const result = await this.spotifyApi.search({
                q: query,
                type: type,
                limit: limit,
                market: this.bot.config.sources.spotify.market || 'US'
            });
            
            const tracks = result.body.tracks?.items.map(track => ({
                title: track.name,
                author: track.artists.map(a => a.name).join(', '),
                url: track.external_urls.spotify,
                thumbnail: track.album.images[0]?.url,
                duration: track.duration_ms,
                source: 'spotify',
                id: track.id,
                album: track.album.name,
                artists: track.artists,
                isrc: track.external_ids?.isrc,
                popularity: track.popularity
            })) || [];
            
            return {
                success: true,
                source: 'spotify',
                query: query,
                tracks: tracks,
                playlist: result.body.playlists?.items || [],
                album: result.body.albums?.items || []
            };
            
        } catch (error) {
            logger.error('Error searching Spotify:', error);
            throw error;
        }
    }

    async searchAppleMusic(query, options = {}) {
        try {
            const appleMusicApi = this.apis.get('appleMusic');
            if (!appleMusicApi) {
                throw new Error('Apple Music API not initialized');
            }
            
            const { limit = 10 } = options;
            
            // Check if query is an Apple Music URL
            if (query.includes('music.apple.com')) {
                return await this.getAppleMusicTrack(query, options);
            }
            
            // Search Apple Music
            const result = await appleMusicApi.search(query, {
                limit: limit,
                types: ['songs', 'albums', 'playlists']
            });
            
            const tracks = result.songs?.data.map(track => ({
                title: track.attributes.name,
                author: track.attributes.artistName,
                url: track.attributes.url,
                thumbnail: track.attributes.artwork?.url.replace('{w}x{h}', '300x300'),
                duration: track.attributes.durationInMillis,
                source: 'apple',
                id: track.id,
                album: track.attributes.albumName,
                genre: track.attributes.genreNames[0],
                isrc: track.attributes.isrc
            })) || [];
            
            return {
                success: true,
                source: 'apple',
                query: query,
                tracks: tracks,
                playlist: result.playlists?.data || [],
                album: result.albums?.data || []
            };
            
        } catch (error) {
            logger.error('Error searching Apple Music:', error);
            throw error;
        }
    }

    async searchYouTube(query, options = {}) {
        try {
            const { limit = 10 } = options;
            
            // Check if query is a YouTube URL
            if (query.includes('youtube.com') || query.includes('youtu.be')) {
                return await this.getYouTubeTrack(query, options);
            }
            
            // Search YouTube using play-dl
            const result = await playdl.search(query, {
                limit: limit,
                type: 'video',
                source: { youtube: 'video' }
            });
            
            const tracks = result.map(video => ({
                title: video.title,
                author: video.channel?.name || 'Unknown',
                url: video.url,
                thumbnail: video.thumbnails[0]?.url,
                duration: video.durationInSec * 1000,
                source: 'youtube',
                id: video.id,
                views: video.views,
                channel: video.channel,
                live: video.live
            })) || [];
            
            return {
                success: true,
                source: 'youtube',
                query: query,
                tracks: tracks
            };
            
        } catch (error) {
            logger.error('Error searching YouTube:', error);
            throw error;
        }
    }

    async searchSoundCloud(query, options = {}) {
        try {
            const { limit = 10 } = options;
            
            // Check if query is a SoundCloud URL
            if (query.includes('soundcloud.com')) {
                return await this.getSoundCloudTrack(query, options);
            }
            
            // Search SoundCloud
            const result = await soundcloud.search({
                q: query,
                limit: limit,
                offset: 0,
                filter: 'streamable'
            });
            
            const tracks = result.map(track => ({
                title: track.title,
                author: track.user?.username || 'Unknown',
                url: track.permalink_url,
                thumbnail: track.artwork_url || track.user?.avatar_url,
                duration: track.duration,
                source: 'soundcloud',
                id: track.id,
                genre: track.genre,
                plays: track.playback_count,
                likes: track.likes_count,
                user: track.user
            })) || [];
            
            return {
                success: true,
                source: 'soundcloud',
                query: query,
                tracks: tracks
            };
            
        } catch (error) {
            logger.error('Error searching SoundCloud:', error);
            throw error;
        }
    }

    async searchDeezer(query, options = {}) {
        try {
            const deezerApi = this.apis.get('deezer');
            if (!deezerApi) {
                throw new Error('Deezer API not initialized');
            }
            
            const { limit = 10 } = options;
            
            // Check if query is a Deezer URL
            if (query.includes('deezer.com')) {
                return await this.getDeezerTrack(query, options);
            }
            
            // Search Deezer
            const result = await deezerApi.search.track(query, { limit: limit });
            
            const tracks = result.data.map(track => ({
                title: track.title,
                author: track.artist.name,
                url: track.link,
                thumbnail: track.album.cover_medium,
                duration: track.duration * 1000,
                source: 'deezer',
                id: track.id,
                album: track.album.title,
                genre: track.genre,
                rank: track.rank,
                explicit: track.explicit_lyrics
            })) || [];
            
            return {
                success: true,
                source: 'deezer',
                query: query,
                tracks: tracks
            };
            
        } catch (error) {
            logger.error('Error searching Deezer:', error);
            throw error;
        }
    }

    async searchTidal(query, options = {}) {
        try {
            const tidalApi = this.apis.get('tidal');
            if (!tidalApi) {
                throw new Error('Tidal API not initialized');
            }
            
            const { limit = 10 } = options;
            
            // Check if query is a Tidal URL
            if (query.includes('tidal.com')) {
                return await this.getTidalTrack(query, options);
            }
            
            // Search Tidal
            const result = await tidalApi.search(query, { limit: limit });
            
            const tracks = result.tracks?.items.map(track => ({
                title: track.title,
                author: track.artists.map(a => a.name).join(', '),
                url: track.url,
                thumbnail: track.album.cover,
                duration: track.duration * 1000,
                source: 'tidal',
                id: track.id,
                album: track.album.title,
                quality: track.audioQuality,
                explicit: track.explicit
            })) || [];
            
            return {
                success: true,
                source: 'tidal',
                query: query,
                tracks: tracks
            };
            
        } catch (error) {
            logger.error('Error searching Tidal:', error);
            throw error;
        }
    }

    async searchBandcamp(query, options = {}) {
        try {
            const { limit = 10 } = options;
            
            // Bandcamp search implementation
            // This is a placeholder - actual implementation would require Bandcamp API
            const tracks = [];
            
            return {
                success: true,
                source: 'bandcamp',
                query: query,
                tracks: tracks,
                message: 'Bandcamp search not yet implemented'
            };
            
        } catch (error) {
            logger.error('Error searching Bandcamp:', error);
            throw error;
        }
    }

    async searchRadio(query, options = {}) {
        try {
            const { limit = 10 } = options;
            
            // Radio search implementation
            // This is a placeholder - actual implementation would require radio API
            const stations = [];
            
            return {
                success: true,
                source: 'radio',
                query: query,
                tracks: stations,
                message: 'Radio search not yet implemented'
            };
            
        } catch (error) {
            logger.error('Error searching Radio:', error);
            throw error;
        }
    }

    async searchTracks(query, source = 'youtube', options = {}) {
        try {
            // Route to appropriate search method based on source
            switch (source.toLowerCase()) {
                case 'youtube':
                    return await this.searchYouTube(query, options);
                case 'spotify':
                    return await this.searchSpotify(query, options);
                case 'apple':
                case 'applemusic':
                    return await this.searchAppleMusic(query, options);
                case 'soundcloud':
                    return await this.searchSoundCloud(query, options);
                case 'deezer':
                    return await this.searchDeezer(query, options);
                case 'tidal':
                    return await this.searchTidal(query, options);
                case 'bandcamp':
                    return await this.searchBandcamp(query, options);
                case 'radio':
                    return await this.searchRadio(query, options);
                default:
                    // Default to YouTube if source not recognized
                    return await this.searchYouTube(query, options);
            }
        } catch (error) {
            logger.error(`Error searching ${source}:`, error);
            throw error;
        }
    }

    async searchAllSources(query, options = {}) {
        try {
            const { limit = 5 } = options;
            const sources = ['youtube', 'spotify', 'apple', 'soundcloud'];
            const results = [];
            
            // Search all sources in parallel
            const searchPromises = sources.map(source => 
                this.searchTracks(query, source, { ...options, limit })
                    .catch(error => {
                        logger.warn(`Error searching ${source}:`, error);
                        return { success: false, source: source, tracks: [] };
                    })
            );
            
            const searchResults = await Promise.all(searchPromises);
            
            // Combine results
            searchResults.forEach(result => {
                if (result.success && result.tracks) {
                    results.push(...result.tracks.slice(0, limit));
                }
            });
            
            return {
                success: true,
                source: 'all',
                query: query,
                tracks: results,
                message: `Found ${results.length} tracks from multiple sources`
            };
            
        } catch (error) {
            logger.error('Error searching all sources:', error);
            throw error;
        }
    }

    async getSpotifyTrack(url, options = {}) {
        try {
            if (!this.lavasfy) {
                throw new Error('Lavasfy not initialized');
            }
            
            const result = await this.lavasfy.request(url);
            
            if (!result || !result.tracks) {
                throw new Error('No tracks found');
            }
            
            const tracks = result.tracks.map(track => ({
                title: track.info.title,
                author: track.info.author,
                url: track.info.uri,
                thumbnail: track.info.artworkUrl,
                duration: track.info.length,
                source: 'spotify',
                id: track.info.identifier,
                isrc: track.info.isrc,
                streamUrl: track.info.streamUrl
            }));
            
            return {
                success: true,
                source: 'spotify',
                tracks: tracks,
                playlist: result.playlistInfo
            };
            
        } catch (error) {
            logger.error('Error getting Spotify track:', error);
            throw error;
        }
    }

    async getAppleMusicTrack(url, options = {}) {
        try {
            const appleMusicApi = this.apis.get('appleMusic');
            if (!appleMusicApi) {
                throw new Error('Apple Music API not initialized');
            }
            
            // Extract track ID from URL
            const trackId = this.extractTrackId(url, 'apple');
            if (!trackId) {
                throw new Error('Invalid Apple Music URL');
            }
            
            const result = await appleMusicApi.getTrack(trackId);
            
            const track = {
                title: result.attributes.name,
                author: result.attributes.artistName,
                url: result.attributes.url,
                thumbnail: result.attributes.artwork.url.replace('{w}x{h}', '300x300'),
                duration: result.attributes.durationInMillis,
                source: 'apple',
                id: result.id,
                album: result.attributes.albumName,
                genre: result.attributes.genreNames[0],
                isrc: result.attributes.isrc
            };
            
            return {
                success: true,
                source: 'apple',
                tracks: [track]
            };
            
        } catch (error) {
            logger.error('Error getting Apple Music track:', error);
            throw error;
        }
    }

    async getYouTubeTrack(url, options = {}) {
        try {
            const result = await playdl.video_info(url);
            
            const track = {
                title: result.video_details.title,
                author: result.video_details.channel.name,
                url: result.video_details.url,
                thumbnail: result.video_details.thumbnails[0]?.url,
                duration: result.video_details.durationInSec * 1000,
                source: 'youtube',
                id: result.video_details.id,
                views: result.video_details.views,
                channel: result.video_details.channel,
                live: result.video_details.live
            };
            
            return {
                success: true,
                source: 'youtube',
                tracks: [track]
            };
            
        } catch (error) {
            logger.error('Error getting YouTube track:', error);
            throw error;
        }
    }

    async getSoundCloudTrack(url, options = {}) {
        try {
            const result = await soundcloud.getInfo(url);
            
            const track = {
                title: result.title,
                author: result.user.username,
                url: result.permalink_url,
                thumbnail: result.artwork_url || result.user.avatar_url,
                duration: result.duration,
                source: 'soundcloud',
                id: result.id,
                genre: result.genre,
                plays: result.playback_count,
                likes: result.likes_count,
                user: result.user
            };
            
            return {
                success: true,
                source: 'soundcloud',
                tracks: [track]
            };
            
        } catch (error) {
            logger.error('Error getting SoundCloud track:', error);
            throw error;
        }
    }

    async getDeezerTrack(url, options = {}) {
        try {
            const deezerApi = this.apis.get('deezer');
            if (!deezerApi) {
                throw new Error('Deezer API not initialized');
            }
            
            // Extract track ID from URL
            const trackId = this.extractTrackId(url, 'deezer');
            if (!trackId) {
                throw new Error('Invalid Deezer URL');
            }
            
            const result = await deezerApi.track(trackId);
            
            const track = {
                title: result.title,
                author: result.artist.name,
                url: result.link,
                thumbnail: result.album.cover_medium,
                duration: result.duration * 1000,
                source: 'deezer',
                id: result.id,
                album: result.album.title,
                genre: result.genre,
                rank: result.rank,
                explicit: result.explicit_lyrics
            };
            
            return {
                success: true,
                source: 'deezer',
                tracks: [track]
            };
            
        } catch (error) {
            logger.error('Error getting Deezer track:', error);
            throw error;
        }
    }

    async getTidalTrack(url, options = {}) {
        try {
            const tidalApi = this.apis.get('tidal');
            if (!tidalApi) {
                throw new Error('Tidal API not initialized');
            }
            
            // Extract track ID from URL
            const trackId = this.extractTrackId(url, 'tidal');
            if (!trackId) {
                throw new Error('Invalid Tidal URL');
            }
            
            const result = await tidalApi.getTrack(trackId);
            
            const track = {
                title: result.title,
                author: result.artists.map(a => a.name).join(', '),
                url: result.url,
                thumbnail: result.album.cover,
                duration: result.duration * 1000,
                source: 'tidal',
                id: result.id,
                album: result.album.title,
                quality: result.audioQuality,
                explicit: result.explicit
            };
            
            return {
                success: true,
                source: 'tidal',
                tracks: [track]
            };
            
        } catch (error) {
            logger.error('Error getting Tidal track:', error);
            throw error;
        }
    }

    extractTrackId(url, source) {
        try {
            switch (source) {
                case 'spotify':
                    const spotifyMatch = url.match(/track\/([a-zA-Z0-9]+)/);
                    return spotifyMatch ? spotifyMatch[1] : null;
                case 'apple':
                    const appleMatch = url.match(/\?i=(\d+)/);
                    return appleMatch ? appleMatch[1] : null;
                case 'deezer':
                    const deezerMatch = url.match(/track\/(\d+)/);
                    return deezerMatch ? deezerMatch[1] : null;
                case 'tidal':
                    const tidalMatch = url.match(/track\/(\d+)/);
                    return tidalMatch ? tidalMatch[1] : null;
                default:
                    return null;
            }
        } catch (error) {
            logger.error('Error extracting track ID:', error);
            return null;
        }
    }

    formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    getSourceStats() {
        return this.sourceStats;
    }

    async shutdown() {
        try {
            logger.info(' Shutting down music service...');
            
            // Clear all APIs and extractors
            this.apis.clear();
            this.extractors.clear();
            this.sources.clear();
            
            // Reset stats
            this.sourceStats = {
                youtube: { searches: 0, tracks: 0, errors: 0 },
                spotify: { searches: 0, tracks: 0, errors: 0 },
                apple: { searches: 0, tracks: 0, errors: 0 },
                soundcloud: { searches: 0, tracks: 0, errors: 0 },
                deezer: { searches: 0, tracks: 0, errors: 0 },
                tidal: { searches: 0, tracks: 0, errors: 0 },
                bandcamp: { searches: 0, tracks: 0, errors: 0 },
                radio: { searches: 0, tracks: 0, errors: 0 }
            };
            
            logger.success(' Music service shut down successfully');
            
        } catch (error) {
            logger.error(' Error shutting down music service:', error);
            throw error;
        }
    }

    async formatTracks(tracks, source, user) {
        const formattedTracks = [];

        for (const track of tracks) {
            const formattedTrack = {
                title: track.title,
                author: track.author,
                url: track.url,
                thumbnail: track.thumbnail,
                duration: track.duration,
                requestedBy: user,
                source: source,
                metadata: {
                    isrc: track.isrc,
                    album: track.album,
                    explicit: track.explicit,
                    genre: track.genre,
                    views: track.views,
                    plays: track.plays,
                    rank: track.rank,
                    popularity: track.popularity,
                    id: track.id,
                    channel: track.channel
                }
            };

            formattedTracks.push(formattedTrack);
        }

        return formattedTracks;
    }

    async getTrackInfo(url) {
        try {
            // Check cache first
            const cacheKey = `track:${url}`;
            const cachedTrack = await this.cache.get(cacheKey);
            
            if (cachedTrack) {
                logger.cacheHit(cacheKey);
                return cachedTrack;
            }

            logger.cacheMiss(cacheKey);

            // Get track info using discord-player
            const track = await this.player.search(url, {
                requestedBy: null
            });

            if (!track.tracks.length) {
                return null;
            }

            const trackInfo = track.tracks[0];
            
            // Cache track info
            await this.cache.set(cacheKey, trackInfo, 3600); // 1 hour

            return trackInfo;

        } catch (error) {
            logger.error('Error getting track info:', error);
            return null;
        }
    }

    async getPlaylistInfo(url) {
        try {
            // Check cache first
            const cacheKey = `playlist:${url}`;
            const cachedPlaylist = await this.cache.get(cacheKey);
            
            if (cachedPlaylist) {
                logger.cacheHit(cacheKey);
                return cachedPlaylist;
            }

            logger.cacheMiss(cacheKey);

            // Get playlist info using discord-player
            const playlist = await this.player.search(url, {
                requestedBy: null,
                searchEngine: 'youtube'
            });

            if (!playlist.playlist) {
                return null;
            }

            const playlistInfo = {
                title: playlist.playlist.title,
                description: playlist.playlist.description,
                thumbnail: playlist.playlist.thumbnail,
                author: playlist.playlist.author.name,
                url: playlist.playlist.url,
                tracks: playlist.tracks,
                trackCount: playlist.tracks.length
            };

            // Cache playlist info
            await this.cache.set(cacheKey, playlistInfo, 1800); // 30 minutes

            return playlistInfo;

        } catch (error) {
            logger.error('Error getting playlist info:', error);
            return null;
        }
    }
}

// Singleton instance
let musicServiceInstance = null;

const getMusicService = (bot) => {
    if (!musicServiceInstance) {
        musicServiceInstance = new MusicService(bot);
    }
    return musicServiceInstance;
};

const searchTracks = async (query, source, options) => {
    if (!musicServiceInstance) {
        throw new Error('MusicService not initialized');
    }
    return await musicServiceInstance.searchTracks(query, source, options);
};

const getTrackInfo = async (url) => {
    if (!musicServiceInstance) {
        throw new Error('MusicService not initialized');
    }
    return await musicServiceInstance.getTrackInfo(url);
};

module.exports = {
    MusicService,
    getMusicService,
    searchTracks,
    getTrackInfo
};
