const { Client, GatewayIntentBits, Partials, Collection } = require('discord.js');
const { Player } = require('discord-player');
const { getAudioService } = require('./services/audioService');
const { getNodeManager } = require('./audio/nodeManager');
const { getMusicService } = require('./services/musicService');
const AudioEnhancementService = require('./services/audioEnhancementService');
const CacheService = require('./cache/cacheService');
// const DatabaseService = require('./database/databaseService');
// const SecurityService = require('./security/securityService');
// const AIService = require('./ai/aiService');
// const PremiumService = require('./premium/premiumService');
const { CommandHandler } = require('./handlers/commandHandler');
const { EventHandler } = require('./handlers/eventHandler');
const logger = require('./utils/logger');
const config = require('./config/config');
const { performance } = require('perf_hooks');
const { token } = require('./config/config');
const { deployCommands } = require('./deploy-commands');

class UltraPremiumMusicBot extends Client {
    constructor() {
        super({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildVoiceStates,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.GuildMessageReactions,
                // Removed privileged intents to avoid "Used disallowed intents" login error
                // GatewayIntentBits.GuildMembers,
                // GatewayIntentBits.GuildPresences,
                GatewayIntentBits.GuildEmojisAndStickers,
                GatewayIntentBits.GuildIntegrations,
                GatewayIntentBits.GuildWebhooks,
                GatewayIntentBits.GuildInvites
            ],
            partials: [
                Partials.User,
                Partials.Channel,
                Partials.Message,
                Partials.Reaction,
                Partials.GuildScheduledEvent,
                Partials.ThreadMember
            ],
            presence: {
                activities: [{
                    name: '🎵 Ultra-Premium Music Experience',
                    type: 3
                }],
                status: 'online'
            }
        });

        this.commands = new Collection();
        this.slashCommands = new Collection();
        this.contextCommands = new Collection();
        this.cooldowns = new Collection();
        this.musicQueues = new Map();
        this.audioNodes = new Map();
        this.premiumUsers = new Set();
        this.serverSettings = new Map();
        this.userSettings = new Map();
        this.aiModels = new Map();
        this.cache = null;
        this.audioEnhancementService = null;
        this.startTime = performance.now();
        // Expose configuration to services (audioService, nodeManager, etc.)
        this.config = config;
        // Expose token for handlers that rely on bot.token
        this.token = config.token;
        
        this.initializeBot();
    }

    async initializeBot() {
        try {
            logger.info('🚀 Initializing Ultra-Premium Discord Music Bot...');
            
            // Initialize core services
            await this.initializeServices();
            
            // Validate token before login (do not print the token)
            if (!token || typeof token !== 'string' || token.length < 20) {
                logger.error('Discord token is missing or appears invalid. Please set DISCORD_TOKEN in .env');
                throw new Error('Invalid or missing DISCORD_TOKEN');
            }

            // Login to Discord
            await this.login(token);
            
            logger.info('✅ Bot successfully initialized and logged in!');
            
        } catch (error) {
            // Log rich diagnostics
            logger.error('❌ Failed to initialize bot:', error);
            console.error('[InitError]', error && (error.stack || error.message || error));
            // Do not exit immediately so users running via start.bat can see the error
            // process.exit(1);
        }
    }

    async initializeServices() {
        try {
            logger.info('🔧 Initializing core services...');
            
            // Initialize cache service
            this.cache = new CacheService(this);
            // await this.cache.connect(); // Assuming connect might not exist or be needed now
            logger.success('✅ Cache service initialized');
            
            // Initialize database service
            // this.database = new DatabaseService(this);
            // await this.database.connect();
            // logger.success('✅ Database service initialized');
            
            // Initialize security service
            // this.security = new SecurityService(this);
            // await this.security.initialize();
            // logger.success('✅ Security service initialized');
            
            // Initialize AI service
            // this.ai = new AIService(this);
            // await this.ai.initialize();
            // logger.success('✅ AI service initialized');
            
            // Initialize premium service
            // this.premium = new PremiumService(this);
            // await this.premium.initialize();
            // logger.success('✅ Premium service initialized');
            
            // Initialize audio service
            this.audioService = getAudioService(this);
            await this.audioService.initializeAudioSystem();
            logger.success('✅ Audio service initialized');
            
            // Initialize node manager (only if Lavalink enabled)
            if (this.config.lavalink.enabled !== false && this.config.lavalink.nodes.length > 0) {
                this.nodeManager = getNodeManager(this);
                logger.success('✅ NodeManager initialized');
            } else {
                logger.info('🔇 NodeManager skipped - Lavalink disabled');
            }
            
            // Initialize music service
            this.musicService = getMusicService(this);
            logger.success('✅ Music service initialized');
            
            // Initialize audio enhancement service
            this.audioEnhancementService = new AudioEnhancementService(this);
            logger.success('✅ Audio enhancement service initialized');
            
            // Initialize command handler
            this.commandHandler = new CommandHandler(this);
            await this.commandHandler.loadCommands();
            logger.success('✅ Command handler initialized');
            
            // Initialize event handler
            this.eventHandler = new EventHandler(this);
            await this.eventHandler.loadEvents();
            logger.success('✅ Event handler initialized');
            
            logger.success('🎉 All services initialized successfully');
            
        } catch (error) {
            logger.error('❌ Error initializing services:', error);
            throw error;
        }
    }

    async loadServerSettings() {
        try {
            const settings = await this.cache.get('server_settings');
            if (settings) {
                this.serverSettings = new Map(Object.entries(settings));
            }
        } catch (error) {
            logger.error('Error loading server settings:', error);
        }
    }

    getUptime() {
        return performance.now() - this.startTime;
    }

    isPremium(userId) {
        return this.premiumUsers.has(userId);
    }

    async addPremiumUser(userId) {
        this.premiumUsers.add(userId);
        await this.cache.set(`premium_user:${userId}`, true, 86400 * 30); // 30 days
        await this.cache.set('premium_users', Array.from(this.premiumUsers));
    }

    async removePremiumUser(userId) {
        this.premiumUsers.delete(userId);
        await this.cache.del(`premium_user:${userId}`);
        await this.cache.set('premium_users', Array.from(this.premiumUsers));
    }

    getServerSettings(guildId) {
        return this.serverSettings.get(guildId) || this.getDefaultServerSettings();
    }

    getDefaultServerSettings() {
        return {
            prefix: '/',
            volume: 100,
            autoPlay: false,
            autoLeave: true,
            autoLeaveTimeout: 300000, // 5 minutes
            defaultFilters: [],
            maxQueueSize: 100,
            allowDuplicates: false,
            announceSongs: true,
            lyricsEnabled: true,
            aiRecommendations: true,
            moodDetection: true,
            multiChannel: false,
            premiumOnly: false
        };
    }
}

// Create bot instance
const bot = new UltraPremiumMusicBot();

// Deploy commands and start bot
async function startBot() {
    try {
        logger.info('🚀 Starting Ultra-Premium Discord Music Bot...');

        // Deploy commands first
        logger.info('📝 Deploying commands...');
        const commandsDeployed = await deployCommands();

        if (commandsDeployed) {
            logger.info('✅ Commands deployed successfully!');
        } else {
            logger.warn('⚠️ Commands deployment failed, but continuing with bot startup...');
        }

        // Start the bot
        logger.info('🤖 Starting bot...');
        await bot.login(token);

    } catch (error) {
        logger.error('❌ Failed to start bot:', error);
        process.exit(1);
    }
}

// Start the bot
startBot();

// Graceful shutdown
process.on('SIGINT', async () => {
    logger.info('🔄 Shutting down gracefully...');
    await bot.destroy();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    logger.info('🔄 Shutting down gracefully...');
    await bot.destroy();
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

module.exports = UltraPremiumMusicBot;
