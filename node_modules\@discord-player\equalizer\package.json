{"name": "@discord-player/equalizer", "version": "0.2.3", "description": "PCM Equalizer implementation for Discord Player", "keywords": ["discord-player", "pcm", "equalizer", "music", "bot", "discord.js", "javascript", "voip", "lavalink", "lavaplayer"], "author": "Androz2091 <<EMAIL>>", "homepage": "https://discord-player.js.org", "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "directories": {"dist": "dist", "src": "src"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/Androz2091/discord-player.git"}, "scripts": {"build": "tsup", "build:check": "tsc --noEmit", "test": "vitest", "coverage": "vitest run --coverage"}, "bugs": {"url": "https://github.com/Androz2091/discord-player/issues"}, "devDependencies": {"@discord-player/tsconfig": "^0.0.0"}, "typedoc": {"entryPoint": "./src/index.ts", "readmeFile": "./README.md", "tsconfig": "./tsconfig.json"}}