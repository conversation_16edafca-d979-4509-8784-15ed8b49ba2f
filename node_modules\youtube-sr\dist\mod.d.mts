interface ChannelIconInterface {
    url?: string;
    width: number;
    height: number;
}
declare class Channel {
    name?: string;
    verified: boolean;
    id?: string;
    url?: string;
    icon: ChannelIconInterface;
    subscribers?: string;
    constructor(data: any);
    private _patch;
    iconURL(options?: {
        size: number;
    }): string;
    get type(): "channel";
    toString(): string;
    toJSON(): {
        name: string;
        verified: boolean;
        id: string;
        url: string;
        iconURL: string;
        type: "channel";
        subscribers: string;
    };
}

type ThumbnailType = "default" | "hqdefault" | "mqdefault" | "sddefault" | "maxresdefault" | "ultrares";
declare class Thumbnail {
    id?: string;
    width: number;
    height: number;
    url?: string;
    constructor(data: any);
    private _patch;
    displayThumbnailURL(thumbnailType?: ThumbnailType): string;
    defaultThumbnailURL(id: "0" | "1" | "2" | "3" | "4"): string;
    toString(): string;
    toJSON(): {
        id: string;
        width: number;
        height: number;
        url: string;
    };
}

interface VideoStreamingData {
    expiresInSeconds: string;
    formats: VideoStreamingFormat[];
    adaptiveFormats: VideoStreamingFormatAdaptive[];
}
interface VideoStreamingFormat {
    itag: number;
    mimeType: string;
    bitrate: number;
    width: number;
    height: number;
    lastModified: string;
    contentLength: string;
    quality: string;
    fps: number;
    qualityLabel: string;
    projectionType: string;
    averageBitrate: number;
    audioQuality: string;
    approxDurationMs: string;
    audioSampleRate: string;
    audioChannels: number;
    signatureCipher: string;
}
interface VideoStreamingFormatAdaptive extends VideoStreamingFormat {
    initRange?: {
        start: string;
        end: string;
    };
    indexRange?: {
        start: string;
        end: string;
    };
    colorInfo?: {
        primaries: string;
        transferCharacteristics?: string;
        matrixCoefficients?: string;
    };
    highReplication?: boolean;
    loudnessDb?: number;
}
interface MusicInfo {
    title: string;
    cover: string;
    artist: string;
    album: string;
}
declare class Video {
    id?: string;
    title?: string;
    description?: string;
    durationFormatted: string;
    duration: number;
    uploadedAt?: string;
    views: number;
    thumbnail?: Thumbnail;
    channel?: Channel;
    videos?: Video[];
    likes: number;
    dislikes: number;
    live: boolean;
    private: boolean;
    tags: string[];
    nsfw: boolean;
    shorts: boolean;
    unlisted: boolean;
    streamingData?: VideoStreamingData | null;
    music: MusicInfo[];
    constructor(data: any);
    private _patch;
    get formats(): VideoStreamingFormat[];
    get adaptiveFormats(): VideoStreamingFormatAdaptive[];
    get url(): string;
    get shortsURL(): string;
    embedHTML(options?: {
        id: string;
        width: number;
        height: number;
    }): string;
    createMixURL(): string;
    get embedURL(): string;
    get type(): "video";
    toString(): string;
    toJSON(): {
        id: string;
        url: string;
        shorts_url: string;
        title: string;
        description: string;
        duration: number;
        duration_formatted: string;
        uploadedAt: string;
        unlisted: boolean;
        nsfw: boolean;
        thumbnail: {
            id: string;
            width: number;
            height: number;
            url: string;
        };
        channel: {
            name: string;
            id: string;
            icon: string;
        };
        views: number;
        type: "video";
        tags: string[];
        ratings: {
            likes: number;
            dislikes: number;
        };
        shorts: boolean;
        live: boolean;
        private: boolean;
        music: MusicInfo[];
    };
}

declare class Playlist {
    id?: string;
    title?: string;
    videoCount: number;
    lastUpdate?: string;
    views?: number;
    url?: string;
    link?: string;
    channel?: Channel;
    thumbnail?: Thumbnail;
    videos: Video[];
    mix?: boolean;
    fake: boolean;
    private _continuation;
    constructor(data?: {}, searchResult?: boolean);
    private _patch;
    private _patchSearch;
    next(limit?: number): Promise<Video[]>;
    fetch(max?: number): Promise<this>;
    get type(): "playlist";
    [Symbol.iterator](): IterableIterator<Video>;
    toJSON(): {
        id: string;
        title: string;
        thumbnail: {
            id: string;
            width: number;
            height: number;
            url: string;
        };
        channel: {
            name: string;
            id: string;
            icon: string;
        };
        mix: boolean;
        url: string;
        videos: Video[];
    };
}

interface ParseSearchInterface {
    type?: "video" | "playlist" | "channel" | "all" | "film";
    limit?: number;
    requestOptions?: RequestInit;
}
declare class Util {
    constructor();
    static innertubeKey(): Promise<string>;
    static get VideoRegex(): RegExp;
    static get VideoIDRegex(): RegExp;
    static get AlbumRegex(): RegExp;
    static get PlaylistURLRegex(): RegExp;
    static get PlaylistIDRegex(): RegExp;
    static fetchInnerTubeKey(): Promise<string>;
    static getHTML(url: string, requestOptions?: RequestInit): Promise<string>;
    static parseDuration(duration: string): number;
    static parseSearchResult(html: string, options?: ParseSearchInterface): (Video | Channel | Playlist)[];
    static parseChannel(data?: any): Channel;
    static parseVideo(data?: any): Video;
    static parsePlaylist(data?: any): Playlist;
    static getPlaylistVideos(data: any, limit?: number): Video[];
    static getPlaylist(html: string, limit?: number): Playlist;
    static getContinuationToken(ctx: any): string;
    static getVideo(html: string): Video;
    static getInfoLikesCount(info: Record<string, any>): number;
    static getNext(body: any, home?: boolean): Video[];
    static getMix(html: string): Playlist;
    static parseHomepage(html: string): Video[];
    static getPlaylistURL(url: string): {
        url: string;
        mix: boolean;
    } | null;
    static validatePlaylist(url: string): void;
    static filter(ftype: string): string;
    static parseMS(milliseconds: number): {
        days: number;
        hours: number;
        minutes: number;
        seconds: number;
    };
    static durationString(data: any): string;
    static json(data: string): any;
    static makeRequest(url?: string, data?: any): Promise<any>;
}

declare class Formatter {
    constructor();
    static formatSearchResult(details: any[], options?: {
        limit?: number;
        type?: "film" | "video" | "channel" | "playlist" | "all";
    }): (Video | Channel | Playlist)[];
}

declare const SAFE_SEARCH_COOKIE = "PREF=f2=8000000";
interface SearchOptions {
    limit?: number;
    type?: "video" | "channel" | "playlist" | "all" | "film";
    requestOptions?: RequestInit;
    safeSearch?: boolean;
}
interface TrendingParseOptions {
    type?: keyof typeof TrendingFilter | "ALL";
}
interface PlaylistOptions {
    limit?: number;
    requestOptions?: RequestInit;
    fetchAll?: boolean;
}
declare const TrendingFilter: {
    MUSIC: string;
    GAMING: string;
    MOVIES: string;
};
declare class YouTube {
    constructor();
    static search(query: string, options?: SearchOptions & {
        type: "video";
    }): Promise<Video[]>;
    static search(query: string, options?: SearchOptions & {
        type: "film";
    }): Promise<Video[]>;
    static search(query: string, options?: SearchOptions & {
        type: "channel";
    }): Promise<Channel[]>;
    static search(query: string, options?: SearchOptions & {
        type: "playlist";
    }): Promise<Playlist[]>;
    static search(query: string, options?: SearchOptions & {
        type: "all";
    }): Promise<(Video | Channel | Playlist)[]>;
    static searchOne(query: string, type?: "video", safeSearch?: boolean, requestOptions?: RequestInit): Promise<Video>;
    static searchOne(query: string, type?: "film", safeSearch?: boolean, requestOptions?: RequestInit): Promise<Video>;
    static searchOne(query: string, type?: "channel", safeSearch?: boolean, requestOptions?: RequestInit): Promise<Channel>;
    static searchOne(query: string, type?: "playlist", safeSearch?: boolean, requestOptions?: RequestInit): Promise<Playlist>;
    static getPlaylist(url: string, options?: PlaylistOptions): Promise<Playlist>;
    static getVideo(url: string | Video, requestOptions?: RequestInit): Promise<Video>;
    static homepage(): Promise<Video[]>;
    static fetchInnerTubeKey(): Promise<string>;
    static trending(options?: TrendingParseOptions): Promise<Video[]>;
    static getSuggestions(query: string): Promise<any[]>;
    static validate(url: string, type?: "VIDEO" | "VIDEO_ID" | "PLAYLIST" | "PLAYLIST_ID"): boolean;
    static isPlaylist(src: string): boolean;
    static get Regex(): {
        PLAYLIST_URL: RegExp;
        PLAYLIST_ID: RegExp;
        ALBUM: RegExp;
        VIDEO_ID: RegExp;
        VIDEO_URL: RegExp;
    };
}

export { Channel, type ChannelIconInterface, Formatter, type MusicInfo, Playlist, type PlaylistOptions, SAFE_SEARCH_COOKIE, type SearchOptions, Thumbnail, TrendingFilter, type TrendingParseOptions, Util, Video, type VideoStreamingData, type VideoStreamingFormat, type VideoStreamingFormatAdaptive, YouTube, YouTube as default };
