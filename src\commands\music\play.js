const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { useQueue } = require('discord-player');
const { checkVoiceChannel, checkBotVoiceChannel, createMusicEmbed } = require('../../utils/musicUtils');
const { getTrackInfo, searchTracks } = require('../../services/musicService');
const { checkCooldown } = require('../../utils/cooldown');
const logger = require('../../utils/logger');

module.exports = {
    name: 'play',
    category: 'music',
    description: 'Play music from various sources',
    usage: '/play <query>',
    
    data: new SlashCommandBuilder()
        .setName('play')
        .setDescription('Play music from various sources')
        .addStringOption(option => 
            option.setName('query')
                .setDescription('The song name, URL, or search query')
                .setRequired(true))
        .addStringOption(option => 
            option.setName('source')
                .setDescription('Music source to search from')
                .addChoices(
                    { name: 'YouTube', value: 'youtube' },
                    { name: 'Spotify', value: 'spotify' },
                    { name: 'Apple Music', value: 'apple' },
                    { name: 'SoundCloud', value: 'soundcloud' },
                    { name: 'Deezer', value: 'deezer' },
                    { name: 'Tidal', value: 'tidal' },
                    { name: 'Bandcamp', value: 'bandcamp' },
                    { name: 'Radio', value: 'radio' }
                )
                .setRequired(false))
        .addStringOption(option => 
            option.setName('filter')
                .setDescription('Apply audio filter')
                .addChoices(
                    { name: 'Bass Boost', value: 'bassboost' },
                    { name: 'Nightcore', value: 'nightcore' },
                    { name: 'Vaporwave', value: 'vaporwave' },
                    { name: 'Pop', value: 'pop' },
                    { name: 'Soft', value: 'soft' },
                    { name: 'Treblebass', value: 'treblebass' },
                    { name: '8D Audio', value: '8d' },
                    { name: 'Karaoke', value: 'karaoke' },
                    { name: 'Vibrato', value: 'vibrato' },
                    { name: 'Tremolo', value: 'tremolo' }
                )
                .setRequired(false))
        .addBooleanOption(option => 
            option.setName('shuffle')
                .setDescription('Shuffle the playlist if multiple tracks are found')
                .setRequired(false))
        .addBooleanOption(option => 
            option.setName('next')
                .setDescription('Add track to the front of the queue')
                .setRequired(false)),

    cooldown: 3,
    permissions: ['Connect', 'Speak'],
    premiumOnly: false,

    async execute(interaction, bot) {
        try {
            const { member, guild, channel } = interaction;
            const query = interaction.options.getString('query');
            const source = interaction.options.getString('source') || 'youtube';
            const filter = interaction.options.getString('filter');
            const shuffle = interaction.options.getBoolean('shuffle') || false;
            const next = interaction.options.getBoolean('next') || false;

            // Check cooldown
            const cooldownCheck = checkCooldown(interaction.user.id, this.name, this.cooldown);
            if (cooldownCheck) {
                return interaction.reply({
                    content: `⏰ Please wait ${cooldownCheck} more seconds before using this command again.`,
                    ephemeral: true
                });
            }

            // Check voice channel
            const voiceCheck = checkVoiceChannel(member);
            if (!voiceCheck.success) {
                return interaction.reply({
                    content: voiceCheck.message,
                    ephemeral: true
                });
            }

            const voiceChannel = voiceCheck.voiceChannel;

            // Check bot voice channel permissions
            const botVoiceCheck = checkBotVoiceChannel(voiceChannel);
            if (!botVoiceCheck.success) {
                return interaction.reply({
                    content: botVoiceCheck.message,
                    ephemeral: true
                });
            }

            // Defer reply since search might take time
            await interaction.deferReply();

            // Search for tracks
            logger.commandExecute(this.name, interaction.user, guild);
            
            const searchResult = await searchTracks(query, source, {
                user: interaction.user,
                guild: guild,
                isPremium: bot.isPremium(interaction.user.id)
            });

            if (!searchResult.success) {
                return interaction.editReply({
                    content: `❌ ${searchResult.message}`,
                    ephemeral: true
                });
            }

            const tracks = searchResult.tracks;

            if (tracks.length === 0) {
                return interaction.editReply({
                    content: '❌ No tracks found for your query.',
                    ephemeral: true
                });
            }

            // Get or create queue
            const queue = useQueue(guild.id);
            let queueCreated = false;

            if (!queue) {
                try {
                    // Create queue and join voice channel
                    const player = bot.audioService?.player || bot.player;
                    if (!player) {
                        return interaction.reply({
                            content: '❌ Audio system not initialized properly.',
                            ephemeral: true
                        });
                    }

                    await player.nodes.create(guild.id, {
                        metadata: {
                            channel: channel,
                            client: guild.members.me,
                            requestedBy: interaction.user
                        },
                        selfDeaf: true,
                        volume: bot.getServerSettings(guild.id).volume,
                        leaveOnEmpty: bot.getServerSettings(guild.id).autoLeave,
                        leaveOnEmptyCooldown: bot.getServerSettings(guild.id).autoLeaveTimeout,
                        leaveOnEnd: bot.getServerSettings(guild.id).autoLeave,
                        leaveOnEndCooldown: bot.getServerSettings(guild.id).autoLeaveTimeout
                    });

                    await bot.player.nodes.get(guild.id).connect(voiceChannel);
                    queueCreated = true;
                } catch (error) {
                    logger.error('Error creating queue:', error);
                    return interaction.editReply({
                        content: '❌ Failed to create music queue. Please try again.',
                        ephemeral: true
                    });
                }
            }

            const currentQueue = bot.player.nodes.get(guild.id);

            // Add tracks to queue
            if (tracks.length === 1) {
                // Single track
                const track = tracks[0];
                
                if (next) {
                    currentQueue.insertTrack(track, 0);
                } else {
                    currentQueue.addTrack(track);
                }

                logger.queueAdd(track, interaction.user, guild);

                // Create embed
                const embed = createMusicEmbed({
                    type: 'trackAdded',
                    track: track,
                    user: interaction.user,
                    queue: currentQueue,
                    position: next ? 1 : currentQueue.getSize()
                });

                await interaction.editReply({ embeds: [embed] });

            } else {
                // Multiple tracks (playlist)
                if (shuffle) {
                    // Shuffle tracks before adding
                    for (let i = tracks.length - 1; i > 0; i--) {
                        const j = Math.floor(Math.random() * (i + 1));
                        [tracks[i], tracks[j]] = [tracks[j], tracks[i]];
                    }
                }

                // Add all tracks
                tracks.forEach(track => {
                    if (next && tracks.indexOf(track) === 0) {
                        currentQueue.insertTrack(track, 0);
                    } else {
                        currentQueue.addTrack(track);
                    }
                    logger.queueAdd(track, interaction.user, guild);
                });

                // Create embed
                const embed = createMusicEmbed({
                    type: 'playlistAdded',
                    tracks: tracks,
                    user: interaction.user,
                    queue: currentQueue,
                    shuffle: shuffle,
                    source: source
                });

                await interaction.editReply({ embeds: [embed] });
            }

            // Apply filter if specified
            if (filter) {
                try {
                    await currentQueue.filters.ffmpeg.setFilters([filter]);
                    logger.audio(`Applied filter: ${filter} in ${guild.name}`);
                } catch (error) {
                    logger.error('Error applying filter:', error);
                }
            }

            // Start playing if not already playing
            if (!currentQueue.isPlaying() && !currentQueue.isPaused()) {
                try {
                    await currentQueue.node.play();
                    logger.trackStart(currentQueue.currentTrack, guild);
                } catch (error) {
                    logger.error('Error starting playback:', error);
                    await interaction.followUp({
                        content: '❌ Failed to start playback. Please try again.',
                        ephemeral: true
                    });
                }
            }

            // Cache track info for faster access
            if (tracks.length === 1) {
                await bot.cache.set(`track:${tracks[0].url}`, tracks[0], 3600); // 1 hour
            }

        } catch (error) {
            logger.error('Error in play command:', error);
            logger.commandError(this.name, error, interaction.user, interaction.guild);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ An error occurred while processing your request.',
                    ephemeral: true
                });
            } else if (interaction.deferred && !interaction.replied) {
                await interaction.editReply({
                    content: '❌ An error occurred while processing your request.',
                    ephemeral: true
                });
            }
        }
    }
};
