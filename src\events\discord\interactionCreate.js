const logger = require('../../utils/logger');

module.exports = {
    name: 'interactionCreate',
    execute(bot, interaction) {
        // Handle slash commands
        if (interaction.isChatInputCommand()) {
            const command = bot.slashCommands.get(interaction.commandName);
            if (!command) return;
            
            try {
                command.execute(interaction, bot);
            } catch (error) {
                logger.error(`<PERSON><PERSON><PERSON> executing slash command ${interaction.commandName}:`, error);
                if (!interaction.replied && !interaction.deferred) {
                    interaction.reply({ content: '❌ There was an error executing that command!', ephemeral: true });
                }
            }
        }
        
        // Handle context menu commands
        if (interaction.isContextMenuCommand()) {
            const command = bot.contextCommands.get(interaction.commandName);
            if (!command) return;
            
            try {
                command.execute(interaction, bot);
            } catch (error) {
                logger.error(`<PERSON>rror executing context command ${interaction.commandName}:`, error);
                if (!interaction.replied && !interaction.deferred) {
                    interaction.reply({ content: '❌ There was an error executing that command!', ephemeral: true });
                }
            }
        }
    }
};
