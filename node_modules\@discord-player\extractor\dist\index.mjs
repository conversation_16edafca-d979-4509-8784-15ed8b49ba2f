var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined")
    return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// src/extractors/SoundCloudExtractor.ts
import {
  BaseExtractor,
  Playlist,
  QueryType,
  Track,
  Util
} from "discord-player";
import * as SoundCloud from "soundcloud.ts";

// src/extractors/common/helper.ts
import { YouTube } from "youtube-sr";
import unfetch from "isomorphic-unfetch";
import http from "http";
import https from "https";
var factory;
var createImport = /* @__PURE__ */ __name((lib) => import(lib).catch(() => null), "createImport");
var UA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.49";
var fetch = unfetch;
var YouTubeLibs = [
  "youtube-ext",
  "ytdl-core",
  "@distube/ytdl-core",
  "play-dl",
  "yt-stream"
  // add more to the list if you have any
];
var ERR_NO_YT_LIB = new Error(`Could not load youtube library. Install one of ${YouTubeLibs.map((lib) => `"${lib}"`).join(", ")}`);
var forcedLib = process.env.DP_FORCE_YTDL_MOD;
if (forcedLib)
  YouTubeLibs.unshift(...forcedLib.split(","));
var httpAgent;
var httpsAgent;
async function loadYtdl(options, force = false) {
  if (factory && !force)
    return factory;
  let lib, _ytLibName, _stream;
  for (const ytlib of YouTubeLibs) {
    lib = await import(ytlib).then(
      (m) => m,
      () => null
    );
    if (!lib)
      continue;
    lib = lib.default || lib;
    _ytLibName = ytlib;
    break;
  }
  if (lib) {
    const isYtdl = ["ytdl-core"].some((lib2) => lib2 === _ytLibName);
    const hlsRegex = /\/manifest\/hls_(variant|playlist)\//;
    _stream = /* @__PURE__ */ __name(async (query, extractor, demuxable = false) => {
      const planner = extractor.context.player.routePlanner;
      const applyPlannerConfig = /* @__PURE__ */ __name((opt, applyAgents = false) => {
        if (planner) {
          try {
            const { ip, family } = planner.getIP();
            if (!applyAgents) {
              opt.requestOptions.localAddress = ip;
              opt.requestOptions.family = family;
            } else {
              const options2 = opt?.requestOptions || {};
              options2.localAddress = ip;
              options2.family = family;
              if (!httpAgent)
                httpAgent = new http.Agent(options2);
              if (!httpsAgent)
                httpsAgent = new https.Agent(options2);
              return Object.assign(opt, {
                requestOptions: options2,
                httpAgent,
                httpsAgent
              });
            }
          } catch {
          }
        }
        return opt;
      }, "applyPlannerConfig");
      if (_ytLibName === "youtube-ext") {
        const dl = lib;
        const opt = applyPlannerConfig(
          {
            ...options,
            requestOptions: options?.requestOptions || {}
          },
          true
        );
        const info = await dl.videoInfo(query, opt);
        const videoFormats = await dl.getFormats(info.stream, opt);
        if (demuxable) {
          const demuxableFormat = info.duration.lengthSec != "0" ? videoFormats.find((fmt2) => {
            return /audio\/webm; codecs="opus"/.test(fmt2.mimeType || "") && fmt2.audioSampleRate == "48000";
          }) : null;
          if (demuxableFormat) {
            return {
              stream: await dl.getReadableStream(demuxableFormat, opt),
              $fmt: "webm/opus"
            };
          }
        }
        const formats = videoFormats.filter((format) => {
          if (!format.url)
            return false;
          if (info.isLive)
            return dl.utils.isHlsContentURL(format.url) && format.url.endsWith(".m3u8");
          return typeof format.bitrate === "number";
        }).sort((a, b) => Number(b.bitrate) - Number(a.bitrate));
        const fmt = formats.find((format) => !format.qualityLabel) || formats.sort((a, b) => Number(a.bitrate) - Number(b.bitrate))[0];
        const url = fmt?.url;
        if (!url)
          throw new Error(`Failed to parse stream url for ${query}`);
        return url;
      } else if (isYtdl) {
        const dl = lib;
        const info = await dl.getInfo(query, applyPlannerConfig(options));
        if (demuxable) {
          const filter = /* @__PURE__ */ __name((format2) => {
            return format2.codecs === "opus" && format2.container === "webm" && format2.audioSampleRate == "48000";
          }, "filter");
          const format = info.formats.find(filter);
          if (format && info.videoDetails.lengthSeconds != "0") {
            return {
              stream: dl.downloadFromInfo(info, {
                ...applyPlannerConfig(options),
                filter
              }),
              $fmt: "webm/opus"
            };
          }
        }
        const formats = info.formats.filter((format) => {
          return info.videoDetails.isLiveContent ? format.isHLS && format.hasAudio : format.hasAudio;
        }).sort((a, b) => Number(b.audioBitrate) - Number(a.audioBitrate) || Number(a.bitrate) - Number(b.bitrate));
        const fmt = formats.find((format) => !format.hasVideo) || formats.sort((a, b) => Number(a.bitrate) - Number(b.bitrate))[0];
        const url = fmt?.url;
        if (!url)
          throw new Error(`Failed to parse stream url for ${query}`);
        return url;
      } else if (_ytLibName === "@distube/ytdl-core") {
        const dl = lib;
        let opt;
        if (planner) {
          opt = {
            localAddress: planner.getIP().ip,
            autoSelectFamily: true
          };
        }
        const cookie = options?.requestOptions?.headers?.cookie;
        const agent = dl.createAgent(Array.isArray(cookie) ? cookie : void 0, opt);
        const reqOpt = {
          agent
        };
        if (cookie && !Array.isArray(cookie)) {
          reqOpt.requestOptions = {
            headers: {
              cookie
            }
          };
        }
        const info = await dl.getInfo(query, reqOpt);
        if (demuxable) {
          const filter = /* @__PURE__ */ __name((format2) => {
            return format2.codecs === "opus" && format2.container === "webm" && format2.audioSampleRate == "48000";
          }, "filter");
          const format = info.formats.find(filter);
          if (format && info.videoDetails.lengthSeconds != "0") {
            return {
              stream: dl.downloadFromInfo(info, {
                ...applyPlannerConfig(options),
                filter
              }),
              $fmt: "webm/opus"
            };
          }
        }
        const formats = info.formats.filter((format) => {
          return info.videoDetails.isLiveContent ? format.isHLS && format.hasAudio : format.hasAudio;
        }).sort((a, b) => Number(b.audioBitrate) - Number(a.audioBitrate) || Number(a.bitrate) - Number(b.bitrate));
        const fmt = formats.find((format) => !format.hasVideo) || formats.sort((a, b) => Number(a.bitrate) - Number(b.bitrate))[0];
        const url = fmt?.url;
        if (!url)
          throw new Error(`Failed to parse stream url for ${query}`);
        return url;
      } else if (_ytLibName === "play-dl") {
        const dl = lib;
        if (typeof options?.requestOptions?.headers?.cookie === "string") {
          dl.setToken({
            youtube: {
              cookie: options.requestOptions.headers.cookie
            }
          });
        }
        const info = await dl.video_info(query);
        if (demuxable) {
          try {
            const stream = await dl.stream(query, {
              discordPlayerCompatibility: false
            });
            return {
              stream: stream.stream,
              $fmt: stream.type
            };
          } catch {
          }
        }
        const formats = info.format.filter((format) => {
          if (!format.url)
            return false;
          if (info.video_details.live)
            return hlsRegex.test(format.url) && typeof format.bitrate === "number" || hlsRegex.test(format.url) && format.url.endsWith(".m3u8");
          return typeof format.bitrate === "number";
        }).sort((a, b) => Number(b.bitrate) - Number(a.bitrate));
        const fmt = formats.find((format) => !format.qualityLabel) || formats.sort((a, b) => Number(a.bitrate) - Number(b.bitrate))[0];
        const url = fmt?.url;
        if (!url)
          throw new Error(`Failed to parse stream url for ${query}`);
        return url;
      } else if (_ytLibName === "yt-stream") {
        const dl = lib;
        const cookie = options?.requestOptions?.headers?.cookie;
        if (cookie && typeof cookie === "string")
          dl.cookie = cookie;
        const decipher = await import("yt-stream/src/stream/decipher.js");
        const info = await dl.getInfo(query);
        info.formats = await decipher?.format_decipher(info.formats, info.html5player);
        const url = info.formats.filter((val) => val.mimeType.startsWith("audio") && val.audioQuality !== "AUDIO_QUALITY_LOW").map((val) => val.url);
        if (url.length !== 0)
          return url[0];
        return info.formats.filter((val) => val.mimeType.startsWith("audio")).map((val) => val.url)[0];
      } else {
        throw ERR_NO_YT_LIB;
      }
    }, "_stream");
  } else {
    throw ERR_NO_YT_LIB;
  }
  factory = { name: _ytLibName, stream: _stream, lib };
  return factory;
}
__name(loadYtdl, "loadYtdl");
async function makeYTSearch(query, opt) {
  const res = await YouTube.search(query, {
    type: "video",
    safeSearch: opt?.safeSearch,
    requestOptions: opt
  }).catch(() => {
  });
  return res || [];
}
__name(makeYTSearch, "makeYTSearch");
async function makeSCSearch(query) {
  const { instance } = SoundCloudExtractor;
  if (!instance?.internal)
    return [];
  let data;
  try {
    const info = await instance.internal.tracks.searchV2({
      q: query,
      limit: 5
    });
    data = info.collection;
  } catch {
    const info = await instance.internal.tracks.searchAlt(query);
    data = info;
  }
  return filterSoundCloudPreviews(data);
}
__name(makeSCSearch, "makeSCSearch");
async function pullYTMetadata(ext, info) {
  const meta = await makeYTSearch(ext.createBridgeQuery(info), "video").then((r) => r[0]).catch(() => null);
  return meta;
}
__name(pullYTMetadata, "pullYTMetadata");
async function pullSCMetadata(ext, info) {
  const meta = await makeSCSearch(ext.createBridgeQuery(info)).then((r) => r[0]).catch(() => null);
  return meta;
}
__name(pullSCMetadata, "pullSCMetadata");
function filterSoundCloudPreviews(tracks) {
  const filtered = tracks.filter((t) => {
    if (typeof t.policy === "string")
      return t.policy.toUpperCase() === "ALLOW";
    return !(t.duration === 3e4 && t.full_duration > 3e4);
  });
  const result = filtered.length > 0 ? filtered : tracks;
  return result;
}
__name(filterSoundCloudPreviews, "filterSoundCloudPreviews");

// src/extractors/SoundCloudExtractor.ts
var _SoundCloudExtractor = class _SoundCloudExtractor extends BaseExtractor {
  constructor() {
    super(...arguments);
    this.internal = new SoundCloud.default({
      clientId: this.options.clientId,
      oauthToken: this.options.oauthToken,
      proxy: this.options.proxy
    });
  }
  async activate() {
    this.protocols = ["scsearch", "soundcloud"];
    _SoundCloudExtractor.instance = this;
  }
  async deactivate() {
    this.protocols = [];
    _SoundCloudExtractor.instance = null;
  }
  async validate(query, type) {
    if (typeof query !== "string")
      return false;
    return [
      QueryType.SOUNDCLOUD,
      QueryType.SOUNDCLOUD_PLAYLIST,
      QueryType.SOUNDCLOUD_SEARCH,
      QueryType.SOUNDCLOUD_TRACK,
      QueryType.AUTO,
      QueryType.AUTO_SEARCH
    ].some((r) => r === type);
  }
  async getRelatedTracks(track, history) {
    if (track.queryType === QueryType.SOUNDCLOUD_TRACK) {
      const data = await this.internal.tracks.relatedV2(track.url, 5);
      const unique = filterSoundCloudPreviews(data).filter((t) => !history.tracks.some((h) => h.url === t.permalink_url));
      return this.createResponse(
        null,
        (unique.length > 0 ? unique : data).map((trackInfo) => {
          const newTrack = new Track(this.context.player, {
            title: trackInfo.title,
            url: trackInfo.permalink_url,
            duration: Util.buildTimeCode(Util.parseMS(trackInfo.duration)),
            description: trackInfo.description ?? "",
            thumbnail: trackInfo.artwork_url,
            views: trackInfo.playback_count,
            author: trackInfo.user.username,
            requestedBy: track.requestedBy,
            source: "soundcloud",
            engine: trackInfo,
            queryType: QueryType.SOUNDCLOUD_TRACK,
            metadata: trackInfo,
            requestMetadata: async () => {
              return trackInfo;
            },
            cleanTitle: trackInfo.title
          });
          newTrack.extractor = this;
          return newTrack;
        })
      );
    }
    return this.createResponse();
  }
  async handle(query, context) {
    if (context.protocol === "scsearch")
      context.type = QueryType.SOUNDCLOUD_SEARCH;
    switch (context.type) {
      case QueryType.SOUNDCLOUD_TRACK: {
        const trackInfo = await this.internal.tracks.getV2(query).catch(Util.noop);
        if (!trackInfo)
          return this.emptyResponse();
        const track = new Track(this.context.player, {
          title: trackInfo.title,
          url: trackInfo.permalink_url,
          duration: Util.buildTimeCode(Util.parseMS(trackInfo.duration)),
          description: trackInfo.description ?? "",
          thumbnail: trackInfo.artwork_url,
          views: trackInfo.playback_count,
          author: trackInfo.user.username,
          requestedBy: context.requestedBy,
          source: "soundcloud",
          engine: trackInfo,
          queryType: context.type,
          metadata: trackInfo,
          requestMetadata: async () => {
            return trackInfo;
          },
          cleanTitle: trackInfo.title
        });
        track.extractor = this;
        return { playlist: null, tracks: [track] };
      }
      case QueryType.SOUNDCLOUD_PLAYLIST: {
        const data = await this.internal.playlists.getV2(query).catch(Util.noop);
        if (!data)
          return { playlist: null, tracks: [] };
        const res = new Playlist(this.context.player, {
          title: data.title,
          description: data.description ?? "",
          thumbnail: data.artwork_url ?? data.tracks[0].artwork_url,
          type: "playlist",
          source: "soundcloud",
          author: {
            name: data.user.username,
            url: data.user.permalink_url
          },
          tracks: [],
          id: `${data.id}`,
          url: data.permalink_url,
          rawPlaylist: data
        });
        for (const song of data.tracks) {
          const track = new Track(this.context.player, {
            title: song.title,
            description: song.description ?? "",
            author: song.user.username,
            url: song.permalink_url,
            thumbnail: song.artwork_url,
            duration: Util.buildTimeCode(Util.parseMS(song.duration)),
            views: song.playback_count,
            requestedBy: context.requestedBy,
            playlist: res,
            source: "soundcloud",
            engine: song,
            queryType: context.type,
            metadata: song,
            requestMetadata: async () => {
              return song;
            },
            cleanTitle: song.title
          });
          track.extractor = this;
          track.playlist = res;
          res.tracks.push(track);
        }
        return { playlist: res, tracks: res.tracks };
      }
      default: {
        let tracks = await this.internal.tracks.searchV2({ q: query }).then((t) => t.collection).catch(Util.noop);
        if (!tracks)
          tracks = await this.internal.tracks.searchAlt(query).catch(Util.noop);
        if (!tracks || !tracks.length)
          return this.emptyResponse();
        tracks = filterSoundCloudPreviews(tracks);
        const resolvedTracks = [];
        for (const trackInfo of tracks) {
          if (!trackInfo.streamable)
            continue;
          const track = new Track(this.context.player, {
            title: trackInfo.title,
            url: trackInfo.permalink_url,
            duration: Util.buildTimeCode(Util.parseMS(trackInfo.duration)),
            description: trackInfo.description ?? "",
            thumbnail: trackInfo.artwork_url,
            views: trackInfo.playback_count,
            author: trackInfo.user.username,
            requestedBy: context.requestedBy,
            source: "soundcloud",
            engine: trackInfo,
            queryType: "soundcloudTrack",
            metadata: trackInfo,
            requestMetadata: async () => {
              return trackInfo;
            }
          });
          track.extractor = this;
          resolvedTracks.push(track);
        }
        return { playlist: null, tracks: resolvedTracks };
      }
    }
  }
  emptyResponse() {
    return { playlist: null, tracks: [] };
  }
  async stream(info) {
    const url = await this.internal.util.streamLink(info.url).catch(Util.noop);
    if (!url)
      throw new Error("Could not extract stream from this track source");
    return url;
  }
  async bridge(track, sourceExtractor) {
    if (sourceExtractor?.identifier === this.identifier) {
      return this.stream(track);
    }
    const query = sourceExtractor?.createBridgeQuery(track) ?? `${track.author} - ${track.title}`;
    const info = await this.handle(query, {
      requestedBy: track.requestedBy,
      type: QueryType.SOUNDCLOUD_SEARCH
    });
    if (!info.tracks.length)
      return null;
    return this.stream(info.tracks[0]);
  }
};
__name(_SoundCloudExtractor, "SoundCloudExtractor");
_SoundCloudExtractor.identifier = "com.discord-player.soundcloudextractor";
_SoundCloudExtractor.instance = null;
var SoundCloudExtractor = _SoundCloudExtractor;

// src/extractors/YoutubeExtractor.ts
import { YouTube as YouTube2 } from "youtube-sr";
import {
  BaseExtractor as BaseExtractor2,
  Playlist as Playlist2,
  QueryType as QueryType2,
  Track as Track2,
  Util as Util2
} from "discord-player";
var validQueryDomains = /* @__PURE__ */ new Set(["youtube.com", "www.youtube.com", "m.youtube.com", "music.youtube.com", "gaming.youtube.com"]);
var validPathDomains = /^https?:\/\/(youtu\.be\/|(www\.)?youtube\.com\/(embed|v|shorts)\/)/;
var idRegex = /^[a-zA-Z0-9-_]{11}$/;
var _YoutubeExtractor = class _YoutubeExtractor extends BaseExtractor2 {
  async activate() {
    this.protocols = ["ytsearch", "youtube"];
    const fn = this.options.createStream;
    if (typeof fn === "function") {
      this._stream = (q) => {
        return fn(this, q);
      };
    } else {
      const { stream, name } = await loadYtdl(this.context.player.options.ytdlOptions);
      this._stream = stream;
      this._ytLibName = name;
    }
    process.emitWarning(
      "YoutubeExtractor uses scraping-based streaming libraries which is known to be unstable at times.\nAn alternative is to use discord-player-youtubei https://npm.im/discord-player-youtubei\nView this GitHub issue for more information: https://github.com/Androz2091/discord-player/issues/1922"
    );
    _YoutubeExtractor.instance = this;
  }
  async deactivate() {
    this.protocols = [];
    _YoutubeExtractor.instance = null;
  }
  async validate(query, type) {
    if (typeof query !== "string")
      return false;
    return [
      QueryType2.YOUTUBE,
      QueryType2.YOUTUBE_PLAYLIST,
      QueryType2.YOUTUBE_SEARCH,
      QueryType2.YOUTUBE_VIDEO,
      QueryType2.AUTO,
      QueryType2.AUTO_SEARCH
    ].some((r) => r === type);
  }
  async handle(query, context) {
    if (context.protocol === "ytsearch")
      context.type = QueryType2.YOUTUBE_SEARCH;
    query = query.includes("youtube.com") ? query.replace(/(m(usic)?|gaming)\./, "") : query;
    if (!query.includes("list=RD") && _YoutubeExtractor.validateURL(query))
      context.type = QueryType2.YOUTUBE_VIDEO;
    switch (context.type) {
      case QueryType2.YOUTUBE_PLAYLIST: {
        const ytpl = await YouTube2.getPlaylist(query, {
          fetchAll: true,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          limit: context.requestOptions?.limit,
          requestOptions: context.requestOptions
        }).catch(Util2.noop);
        if (!ytpl)
          return this.emptyResponse();
        const playlist = new Playlist2(this.context.player, {
          title: ytpl.title,
          thumbnail: ytpl.thumbnail?.displayThumbnailURL("maxresdefault"),
          description: ytpl.title || "",
          type: "playlist",
          source: "youtube",
          author: {
            name: ytpl.channel.name,
            url: ytpl.channel.url
          },
          tracks: [],
          id: ytpl.id,
          url: ytpl.url,
          rawPlaylist: ytpl
        });
        playlist.tracks = ytpl.videos.map((video) => {
          const track = new Track2(this.context.player, {
            title: video.title,
            description: video.description,
            author: video.channel?.name,
            url: video.url,
            requestedBy: context.requestedBy,
            thumbnail: video.thumbnail.url,
            views: video.views,
            duration: video.durationFormatted,
            raw: video,
            playlist,
            source: "youtube",
            queryType: "youtubeVideo",
            metadata: video,
            async requestMetadata() {
              return video;
            },
            live: video.live
          });
          track.extractor = this;
          track.playlist = playlist;
          return track;
        });
        return { playlist, tracks: playlist.tracks };
      }
      case QueryType2.YOUTUBE_VIDEO: {
        const id = /[a-zA-Z0-9-_]{11}/.exec(query);
        if (!id?.[0])
          return this.emptyResponse();
        const video = await YouTube2.getVideo(`https://www.youtube.com/watch?v=${id}`, context.requestOptions).catch(Util2.noop);
        if (!video)
          return this.emptyResponse();
        video.source = "youtube";
        const track = new Track2(this.context.player, {
          title: video.title,
          description: video.description,
          author: video.channel?.name,
          url: video.url,
          requestedBy: context.requestedBy,
          thumbnail: video.thumbnail?.displayThumbnailURL("maxresdefault"),
          views: video.views,
          duration: video.durationFormatted,
          source: "youtube",
          raw: video,
          queryType: context.type,
          metadata: video,
          async requestMetadata() {
            return video;
          },
          live: video.live
        });
        track.extractor = this;
        return {
          playlist: null,
          tracks: [track]
        };
      }
      default: {
        const tracks = await this._makeYTSearch(query, context);
        return { playlist: null, tracks };
      }
    }
  }
  async _makeYTSearch(query, context) {
    const res = await makeYTSearch(query, context.requestOptions).catch(Util2.noop);
    if (!res || !res.length)
      return [];
    return res.map((video) => {
      video.source = "youtube";
      const track = new Track2(this.context.player, {
        title: video.title,
        description: video.description,
        author: video.channel?.name,
        url: video.url,
        requestedBy: context.requestedBy,
        thumbnail: video.thumbnail?.displayThumbnailURL("maxresdefault"),
        views: video.views,
        duration: video.durationFormatted,
        source: "youtube",
        raw: video,
        queryType: context.type,
        metadata: video,
        async requestMetadata() {
          return video;
        },
        live: video.live
      });
      track.extractor = this;
      return track;
    });
  }
  async getRelatedTracks(track, history) {
    let info = void 0;
    if (_YoutubeExtractor.validateURL(track.url))
      info = await YouTube2.getVideo(track.url).then((x) => x.videos).catch(Util2.noop);
    if (!info)
      info = await YouTube2.search(track.author || track.title, { limit: 5, type: "video" }).then((x) => x).catch(Util2.noop);
    if (!info?.length) {
      return this.createResponse();
    }
    const unique = info.filter((t) => !history.tracks.some((x) => x.url === t.url));
    const similar = (unique.length > 0 ? unique : info).map((video) => {
      const t = new Track2(this.context.player, {
        title: video.title,
        url: `https://www.youtube.com/watch?v=${video.id}`,
        duration: video.durationFormatted || Util2.buildTimeCode(Util2.parseMS(video.duration * 1e3)),
        description: video.title,
        thumbnail: typeof video.thumbnail === "string" ? video.thumbnail : video.thumbnail.url,
        views: video.views,
        author: video.channel.name,
        requestedBy: track.requestedBy,
        source: "youtube",
        queryType: "youtubeVideo",
        metadata: video,
        async requestMetadata() {
          return video;
        },
        live: video.live
      });
      t.extractor = this;
      return t;
    });
    return this.createResponse(null, similar);
  }
  emptyResponse() {
    return { playlist: null, tracks: [] };
  }
  async stream(info) {
    if (!this._stream) {
      throw new Error(`Could not find youtube streaming library. Install one of ${YouTubeLibs.join(", ")}`);
    }
    let url = info.url;
    url = url.includes("youtube.com") ? url.replace(/(m(usic)?|gaming)\./, "") : url;
    return this._stream(url, this, this.supportsDemux);
  }
  async bridge(track, sourceExtractor) {
    if (sourceExtractor?.identifier === this.identifier) {
      return this.stream(track);
    }
    const query = sourceExtractor?.createBridgeQuery(track) ?? `${track.author} - ${track.title}`;
    const info = await this.handle(query, {
      requestedBy: track.requestedBy,
      type: QueryType2.YOUTUBE
    });
    if (!info.tracks.length)
      return null;
    return this.stream(info.tracks[0]);
  }
  static validateURL(link) {
    try {
      _YoutubeExtractor.parseURL(link);
      return true;
    } catch {
      return false;
    }
  }
  static validateId(id) {
    return idRegex.test(id.trim());
  }
  static parseURL(link) {
    const parsed = new URL(link.trim());
    let id = parsed.searchParams.get("v");
    if (validPathDomains.test(link.trim()) && !id) {
      const paths = parsed.pathname.split("/");
      id = parsed.host === "youtu.be" ? paths[1] : paths[2];
    } else if (parsed.hostname && !validQueryDomains.has(parsed.hostname)) {
      throw Error("Not a YouTube domain");
    }
    if (!id) {
      throw Error(`No video id found: "${link}"`);
    }
    id = id.substring(0, 11);
    if (!this.validateId(id)) {
      throw TypeError(`Video id (${id}) does not match expected format (${idRegex.toString()})`);
    }
    return id;
  }
};
__name(_YoutubeExtractor, "YoutubeExtractor");
_YoutubeExtractor.identifier = "com.discord-player.youtubeextractor";
var YoutubeExtractor = _YoutubeExtractor;

// src/extractors/LyricsExtractor.ts
import { Client as GeniusClient } from "genius-lyrics";
var client;
function lyricsExtractor(apiKey, force) {
  if (!client && !force)
    client = new GeniusClient(apiKey);
  return { search, client };
}
__name(lyricsExtractor, "lyricsExtractor");
function search(query) {
  return new Promise((resolve, reject) => {
    if (typeof query !== "string")
      return reject(new TypeError(`Expected search query to be a string, received "${typeof query}"!`));
    client.songs.search(query).then(async (songs) => {
      const data = {
        title: songs[0].title,
        fullTitle: songs[0].fullTitle,
        id: songs[0].id,
        thumbnail: songs[0].thumbnail,
        image: songs[0].image,
        url: songs[0].url,
        artist: {
          name: songs[0].artist.name,
          id: songs[0].artist.id,
          url: songs[0].artist.url,
          image: songs[0].artist.image
        },
        lyrics: await songs[0].lyrics(false)
      };
      resolve(data);
    }).catch(() => {
      reject(new Error("Could not parse lyrics"));
    });
  });
}
__name(search, "search");

// src/extractors/VimeoExtractor.ts
import {
  BaseExtractor as BaseExtractor3,
  QueryType as QueryType3,
  Track as Track3,
  Util as Util3
} from "discord-player";

// src/internal/Vimeo.ts
import http2 from "http";
import https2 from "https";
var _Vimeo = class _Vimeo {
  constructor() {
    throw new Error(`The ${this.constructor.name} class may not be instantiated!`);
  }
  /**
   * @typedef {Readable} Readable
   */
  /**
   * Downloads from vimeo
   * @param {number} id Vimeo video id
   * @returns {Promise<Readable>}
   */
  static download(id) {
    return new Promise(async (resolve) => {
      const info = await _Vimeo.getInfo(id);
      if (!info)
        return null;
      const downloader = info.stream.startsWith("https://") ? https2 : http2;
      downloader.get(info.stream, (res) => {
        resolve(res);
      });
    });
  }
  /**
   * Returns video info
   * @param {number} id Video id
   */
  static async getInfo(id) {
    if (!id)
      throw new Error("Invalid id");
    const url = `https://player.vimeo.com/video/${id}`;
    try {
      const res = await fetch(url);
      const data = await res.text();
      const json = JSON.parse(data.split("window.playerConfig =")[1].split(";")[0].trim());
      const obj = {
        id: json.video.id,
        duration: json.video.duration * 1e3,
        title: json.video.title,
        url: json.video.url,
        thumbnail: json.video.thumbs["1280"] || json.video.thumbs.base,
        stream: json.request.files.progressive[0].url,
        author: {
          id: json.video.owner.id,
          name: json.video.owner.name,
          url: json.video.owner.url,
          avatar: json.video.owner.img_2x || json.video.owner.img
        }
      };
      return obj;
    } catch {
      return null;
    }
  }
};
__name(_Vimeo, "Vimeo");
var Vimeo = _Vimeo;

// src/extractors/VimeoExtractor.ts
var _VimeoExtractor = class _VimeoExtractor extends BaseExtractor3 {
  async validate(query, type) {
    if (typeof query !== "string")
      return false;
    return [QueryType3.VIMEO].some((r) => r === type);
  }
  async getRelatedTracks(track) {
    return this.createResponse();
  }
  async handle(query, context) {
    switch (context.type) {
      case QueryType3.VIMEO: {
        const trackInfo = await Vimeo.getInfo(
          query.split("/").filter((x) => !!x).pop()
        ).catch(Util3.noop);
        if (!trackInfo)
          return this.emptyResponse();
        const track = new Track3(this.context.player, {
          title: trackInfo.title,
          url: trackInfo.url,
          duration: Util3.buildTimeCode(Util3.parseMS(trackInfo.duration || 0)),
          description: `${trackInfo.title} by ${trackInfo.author.name}`,
          thumbnail: trackInfo.thumbnail,
          views: 0,
          author: trackInfo.author.name,
          requestedBy: context.requestedBy,
          source: "arbitrary",
          engine: trackInfo.stream,
          queryType: context.type,
          metadata: trackInfo,
          async requestMetadata() {
            return trackInfo;
          }
        });
        track.extractor = this;
        return { playlist: null, tracks: [track] };
      }
      default:
        return this.emptyResponse();
    }
  }
  emptyResponse() {
    return { playlist: null, tracks: [] };
  }
  async stream(info) {
    const engine = info.raw.engine;
    if (engine) {
      return engine;
    }
    const track = await Vimeo.getInfo(info.url).catch(Util3.noop);
    if (!track || !track.stream)
      throw new Error("Could not extract stream from this source");
    info.raw.engine = {
      streamURL: track.stream
    };
    return track.stream;
  }
};
__name(_VimeoExtractor, "VimeoExtractor");
_VimeoExtractor.identifier = "com.discord-player.vimeoextractor";
var VimeoExtractor = _VimeoExtractor;

// src/extractors/ReverbnationExtractor.ts
import {
  BaseExtractor as BaseExtractor4,
  QueryType as QueryType4,
  Track as Track4,
  Util as Util4
} from "discord-player";
import reverbnation from "reverbnation-scraper";
var _ReverbnationExtractor = class _ReverbnationExtractor extends BaseExtractor4 {
  async validate(query, type) {
    if (typeof query !== "string")
      return false;
    return [QueryType4.REVERBNATION].some((r) => r === type);
  }
  async getRelatedTracks(track) {
    return this.createResponse();
  }
  async handle(query, context) {
    switch (context.type) {
      case QueryType4.REVERBNATION: {
        const trackInfo = await reverbnation.getInfo(query).catch(Util4.noop);
        if (!trackInfo)
          return this.emptyResponse();
        const track = new Track4(this.context.player, {
          title: trackInfo.title,
          url: trackInfo.url,
          duration: Util4.buildTimeCode(Util4.parseMS(trackInfo.duration)),
          description: trackInfo.lyrics || `${trackInfo.title} by ${trackInfo.artist.name}`,
          thumbnail: trackInfo.thumbnail,
          views: 0,
          author: trackInfo.artist.name,
          requestedBy: context.requestedBy,
          source: "arbitrary",
          engine: trackInfo.streamURL,
          queryType: context.type,
          metadata: trackInfo,
          async requestMetadata() {
            return trackInfo;
          }
        });
        track.extractor = this;
        return { playlist: null, tracks: [track] };
      }
      default:
        return this.emptyResponse();
    }
  }
  emptyResponse() {
    return { playlist: null, tracks: [] };
  }
  async stream(info) {
    const engine = info.raw.engine;
    if (engine) {
      return engine;
    }
    const track = await reverbnation.getInfo(info.url).catch(Util4.noop);
    if (!track || !track.streamURL)
      throw new Error("Could not extract stream from this source");
    info.raw.engine = {
      streamURL: track.streamURL
    };
    return track.streamURL;
  }
};
__name(_ReverbnationExtractor, "ReverbnationExtractor");
_ReverbnationExtractor.identifier = "com.discord-player.reverbnationextractor";
var ReverbnationExtractor = _ReverbnationExtractor;

// src/extractors/AttachmentExtractor.ts
import {
  BaseExtractor as BaseExtractor5,
  QueryType as QueryType5,
  Track as Track5,
  Util as Util5
} from "discord-player";
import { createReadStream, existsSync } from "fs";

// src/internal/downloader.ts
import http3 from "http";
import https3 from "https";
function downloadStream(url, opts = {}) {
  return new Promise((resolve, reject) => {
    const lib = url.startsWith("http://") ? http3 : https3;
    lib.get(url, opts, (res) => resolve(res)).once("error", reject);
  });
}
__name(downloadStream, "downloadStream");

// src/extractors/AttachmentExtractor.ts
import * as fileType from "file-type";
import path from "path";
import { stat } from "fs/promises";
var ATTACHMENT_HEADER = ["audio/", "video/", "application/ogg"];
var _AttachmentExtractor = class _AttachmentExtractor extends BaseExtractor5 {
  constructor() {
    super(...arguments);
    // use lowest priority to avoid conflict with other extractors
    this.priority = 0;
  }
  async validate(query, type) {
    if (typeof query !== "string")
      return false;
    return [QueryType5.ARBITRARY, QueryType5.FILE].some((r) => r === type);
  }
  async getRelatedTracks(track) {
    return this.createResponse();
  }
  async handle(query, context) {
    switch (context.type) {
      case QueryType5.ARBITRARY: {
        const data = await downloadStream(query, context.requestOptions);
        if (!ATTACHMENT_HEADER.some((r) => !!data.headers["content-type"]?.startsWith(r)))
          return this.emptyResponse();
        const trackInfo = {
          title: (query.split("/").filter((x) => x.length).pop() ?? "Attachment").split("?")[0].trim(),
          duration: 0,
          thumbnail: "https://upload.wikimedia.org/wikipedia/commons/2/2a/ITunes_12.2_logo.png",
          engine: query,
          // eslint-disable-next-line
          author: data.client?.servername || "Attachment",
          // eslint-disable-next-line
          description: data.client?.servername || "Attachment",
          url: data.url || query
        };
        try {
          const mediaplex = __require("mediaplex");
          const timeout = this.context.player.options.probeTimeout ?? 5e3;
          const { result, stream } = await Promise.race([
            mediaplex.probeStream(data),
            new Promise((_, r) => {
              setTimeout(() => r(new Error("Timeout")), timeout);
            })
          ]);
          if (result) {
            trackInfo.duration = result.duration * 1e3;
            const metadata = mediaplex.readMetadata(result);
            if (metadata.author)
              trackInfo.author = metadata.author;
            if (metadata.title)
              trackInfo.title = metadata.title;
            trackInfo.description = `${trackInfo.title} by ${trackInfo.author}`;
          }
          stream.destroy();
        } catch {
        }
        const track = new Track5(this.context.player, {
          title: trackInfo.title,
          url: trackInfo.url,
          duration: Util5.buildTimeCode(Util5.parseMS(trackInfo.duration)),
          description: trackInfo.description,
          thumbnail: trackInfo.thumbnail,
          views: 0,
          author: trackInfo.author,
          requestedBy: context.requestedBy,
          source: "arbitrary",
          engine: trackInfo.url,
          queryType: context.type,
          metadata: trackInfo,
          async requestMetadata() {
            return trackInfo;
          }
        });
        track.extractor = this;
        track.raw.isFile = false;
        return { playlist: null, tracks: [track] };
      }
      case QueryType5.FILE: {
        if (!existsSync(query))
          return this.emptyResponse();
        const fstat = await stat(query);
        if (!fstat.isFile())
          return this.emptyResponse();
        const mime = await fileType.fromFile(query).catch(() => null);
        if (!mime || !ATTACHMENT_HEADER.some((r) => !!mime.mime.startsWith(r)))
          return this.emptyResponse();
        const trackInfo = {
          title: path.basename(query) || "Attachment",
          duration: 0,
          thumbnail: "https://upload.wikimedia.org/wikipedia/commons/2/2a/ITunes_12.2_logo.png",
          engine: query,
          author: "Attachment",
          description: "Attachment",
          url: query
        };
        try {
          const mediaplex = __require("mediaplex");
          const timeout = this.context.player.options.probeTimeout ?? 5e3;
          const { result, stream } = await Promise.race([
            mediaplex.probeStream(
              createReadStream(query, {
                start: 0,
                end: 1024 * 1024 * 10
              })
            ),
            new Promise((_, r) => {
              setTimeout(() => r(new Error("Timeout")), timeout);
            })
          ]);
          if (result) {
            trackInfo.duration = result.duration * 1e3;
            const metadata = mediaplex.readMetadata(result);
            if (metadata.author)
              trackInfo.author = metadata.author;
            if (metadata.title)
              trackInfo.title = metadata.title;
            trackInfo.description = `${trackInfo.title} by ${trackInfo.author}`;
          }
          stream.destroy();
        } catch {
        }
        const track = new Track5(this.context.player, {
          title: trackInfo.title,
          url: trackInfo.url,
          duration: Util5.buildTimeCode(Util5.parseMS(trackInfo.duration)),
          description: trackInfo.description,
          thumbnail: trackInfo.thumbnail,
          views: 0,
          author: trackInfo.author,
          requestedBy: context.requestedBy,
          source: "arbitrary",
          engine: trackInfo.url,
          queryType: context.type,
          metadata: trackInfo,
          async requestMetadata() {
            return trackInfo;
          }
        });
        track.extractor = this;
        track.raw.isFile = true;
        return { playlist: null, tracks: [track] };
      }
      default:
        return this.emptyResponse();
    }
  }
  emptyResponse() {
    return { playlist: null, tracks: [] };
  }
  async stream(info) {
    const engine = info.raw.engine;
    const isFile = info.raw.isFile;
    if (!engine)
      throw new Error("Could not find stream source");
    if (!isFile) {
      return engine;
    }
    return createReadStream(engine);
  }
};
__name(_AttachmentExtractor, "AttachmentExtractor");
_AttachmentExtractor.identifier = "com.discord-player.attachmentextractor";
var AttachmentExtractor = _AttachmentExtractor;

// src/extractors/AppleMusicExtractor.ts
import { Playlist as Playlist3, QueryType as QueryType6, Track as Track6, Util as Util6 } from "discord-player";

// src/internal/index.ts
var internal_exports = {};
__export(internal_exports, {
  AppleMusic: () => AppleMusic,
  SpotifyAPI: () => SpotifyAPI,
  Vimeo: () => Vimeo,
  downloadStream: () => downloadStream
});

// src/internal/AppleMusic.ts
import { QueryResolver } from "discord-player";
import { parse } from "node-html-parser";
function getHTML(link) {
  return fetch(link, {
    headers: {
      "User-Agent": UA
    }
  }).then((r) => r.text()).then(
    (txt) => parse(txt),
    () => null
  );
}
__name(getHTML, "getHTML");
function makeImage({ height, url, width, ext = "jpg" }) {
  return url.replace("{w}", `${width}`).replace("{h}", `${height}`).replace("{f}", ext);
}
__name(makeImage, "makeImage");
function parseDuration(d) {
  const r = /* @__PURE__ */ __name((name, unit) => `((?<${name}>-?\\d*[\\.,]?\\d+)${unit})?`, "r");
  const regex = new RegExp(
    [
      "(?<negative>-)?P",
      r("years", "Y"),
      r("months", "M"),
      r("weeks", "W"),
      r("days", "D"),
      "(T",
      r("hours", "H"),
      r("minutes", "M"),
      r("seconds", "S"),
      ")?"
      // end optional time
    ].join("")
  );
  const test = regex.exec(d);
  if (!test || !test.groups)
    return "0:00";
  const dur = [test.groups.years, test.groups.months, test.groups.weeks, test.groups.days, test.groups.hours, test.groups.minutes, test.groups.seconds];
  return dur.filter((r2, i, a) => !!r2 || i > a.length - 2).map((m, i) => {
    if (!m)
      m = "0";
    return i < 1 ? m : m.padStart(2, "0");
  }).join(":") || "0:00";
}
__name(parseDuration, "parseDuration");
var _AppleMusic = class _AppleMusic {
  constructor() {
    return _AppleMusic;
  }
  static async search(query) {
    try {
      const url = `https://music.apple.com/us/search?term=${encodeURIComponent(query)}`;
      const node = await getHTML(url);
      if (!node)
        return [];
      const rawData = node.getElementById("serialized-server-data");
      if (!rawData)
        return [];
      const data = JSON.parse(rawData.innerText)[0].data.sections;
      const tracks = data.find((s) => s.itemKind === "trackLockup")?.items;
      if (!tracks)
        return [];
      return tracks.map((track) => ({
        id: track.contentDescriptor.identifiers.storeAdamID,
        duration: track.duration || "0:00",
        title: track.title,
        url: track.contentDescriptor.url,
        thumbnail: track?.artwork?.dictionary ? makeImage({
          url: track.artwork.dictionary.url,
          height: track.artwork.dictionary.height,
          width: track.artwork.dictionary.width
        }) : "https://music.apple.com/assets/favicon/favicon-180.png",
        artist: {
          name: track.subtitleLinks?.[0]?.title ?? "Unknown Artist"
        }
      }));
    } catch {
      return [];
    }
  }
  static async getSongInfoFallback(res, name, id, link) {
    try {
      const metaTags = res.getElementsByTagName("meta");
      if (!metaTags.length)
        return null;
      const title = metaTags.find((r) => r.getAttribute("name") === "apple:title")?.getAttribute("content") || res.querySelector("title")?.innerText || name;
      const contentId = metaTags.find((r) => r.getAttribute("name") === "apple:content_id")?.getAttribute("content") || id;
      const durationRaw = metaTags.find((r) => r.getAttribute("property") === "music:song:duration")?.getAttribute("content");
      const song = {
        id: contentId,
        duration: durationRaw ? parseDuration(durationRaw) : metaTags.find((m) => m.getAttribute("name") === "apple:description")?.textContent.split("Duration: ")?.[1].split('"')?.[0] || "0:00",
        title,
        url: link,
        thumbnail: metaTags.find((r) => ["og:image:secure_url", "og:image"].includes(r.getAttribute("property")))?.getAttribute("content") || "https://music.apple.com/assets/favicon/favicon-180.png",
        artist: {
          name: res.querySelector(".song-subtitles__artists>a")?.textContent?.trim() || "Apple Music"
        }
      };
      return song;
    } catch {
      return null;
    }
  }
  static async getSongInfo(link) {
    if (!QueryResolver.regex.appleMusicSongRegex.test(link)) {
      return null;
    }
    const url = new URL(link);
    const id = url.searchParams.get("i");
    const name = url.pathname.split("album/")[1]?.split("/")[0];
    if (!id || !name)
      return null;
    const res = await getHTML(`https://music.apple.com/us/song/${name}/${id}`);
    if (!res)
      return null;
    try {
      const datasrc = res.getElementById("serialized-server-data")?.innerText || res.innerText.split('<script type="application/json" id="serialized-server-data">')?.[1]?.split("</script>")?.[0];
      if (!datasrc)
        throw "not found";
      const data = JSON.parse(datasrc)[0].data.seoData;
      const song = data.ogSongs[0]?.attributes;
      return {
        id: data.ogSongs[0]?.id || data.appleContentId || id,
        duration: song?.durationInMillis || "0:00",
        title: song?.name || data.appleTitle,
        url: song?.url || data.url || link,
        thumbnail: song?.artwork ? makeImage({
          url: song.artwork.url,
          height: song.artwork.height,
          width: song.artwork.width
        }) : data.artworkUrl ? makeImage({
          height: data.height,
          width: data.width,
          url: data.artworkUrl,
          ext: data.fileType || "jpg"
        }) : "https://music.apple.com/assets/favicon/favicon-180.png",
        artist: {
          name: song?.artistName || data.socialTitle || "Apple Music"
        }
      };
    } catch {
      return this.getSongInfoFallback(res, name, id, link);
    }
  }
  static async getPlaylistInfo(link) {
    if (!QueryResolver.regex.appleMusicPlaylistRegex.test(link)) {
      return null;
    }
    const res = await getHTML(link);
    if (!res)
      return null;
    try {
      const datasrc = res.getElementById("serialized-server-data")?.innerText || res.innerText.split('<script type="application/json" id="serialized-server-data">')?.[1]?.split("</script>")?.[0];
      if (!datasrc)
        throw "not found";
      const pl = JSON.parse(datasrc)[0].data.seoData;
      const thumbnail = pl.artworkUrl ? makeImage({
        height: pl.height,
        width: pl.width,
        url: pl.artworkUrl,
        ext: pl.fileType || "jpg"
      }) : "https://music.apple.com/assets/favicon/favicon-180.png";
      return {
        id: pl.appleContentId,
        title: pl.appleTitle,
        thumbnail,
        artist: {
          name: pl.ogSongs?.[0]?.attributes?.artistName || "Apple Music"
        },
        url: pl.url,
        tracks: (
          // eslint-disable-next-line
          pl.ogSongs?.map((m) => {
            const song = m.attributes;
            return {
              id: m.id,
              duration: song.durationInMillis || "0:00",
              title: song.name,
              url: song.url,
              thumbnail: song.artwork ? makeImage({
                url: song.artwork.url,
                height: song.artwork.height,
                width: song.artwork.width
              }) : thumbnail,
              artist: {
                name: song.artistName || "Apple Music"
              }
            };
          }) || []
        )
      };
    } catch {
      return null;
    }
  }
  static async getAlbumInfo(link) {
    if (!QueryResolver.regex.appleMusicAlbumRegex.test(link)) {
      return null;
    }
    const res = await getHTML(link);
    if (!res)
      return null;
    try {
      const datasrc = res.getElementById("serialized-server-data")?.innerText || res.innerText.split('<script type="application/json" id="serialized-server-data">')?.[1]?.split("</script>")?.[0];
      if (!datasrc)
        throw "not found";
      const pl = JSON.parse(datasrc)[0].data.seoData;
      const thumbnail = pl.artworkUrl ? makeImage({
        height: pl.height,
        width: pl.width,
        url: pl.artworkUrl,
        ext: pl.fileType || "jpg"
      }) : "https://music.apple.com/assets/favicon/favicon-180.png";
      return {
        id: pl.appleContentId,
        title: pl.appleTitle,
        thumbnail,
        artist: {
          name: pl.ogSongs?.[0]?.attributes?.artistName || "Apple Music"
        },
        url: pl.url,
        tracks: (
          // eslint-disable-next-line
          pl.ogSongs?.map((m) => {
            const song = m.attributes;
            return {
              id: m.id,
              duration: song.durationInMillis || "0:00",
              title: song.name,
              url: song.url,
              thumbnail: song.artwork ? makeImage({
                url: song.artwork.url,
                height: song.artwork.height,
                width: song.artwork.width
              }) : thumbnail,
              artist: {
                name: song.artistName || "Apple Music"
              }
            };
          }) || []
        )
      };
    } catch {
      return null;
    }
  }
};
__name(_AppleMusic, "AppleMusic");
var AppleMusic = _AppleMusic;

// src/internal/Spotify.ts
var SP_ANON_TOKEN_URL = "https://open.spotify.com/get_access_token?reason=transport&productType=embed";
var SP_ACCESS_TOKEN_URL = "https://accounts.spotify.com/api/token?grant_type=client_credentials";
var SP_BASE = "https://api.spotify.com/v1";
var _SpotifyAPI = class _SpotifyAPI {
  constructor(credentials = {
    clientId: null,
    clientSecret: null
  }) {
    this.credentials = credentials;
    this.accessToken = null;
  }
  get authorizationKey() {
    if (!this.credentials.clientId || !this.credentials.clientSecret)
      return null;
    return Buffer.from(`${this.credentials.clientId}:${this.credentials.clientSecret}`).toString("base64");
  }
  async requestToken() {
    const key = this.authorizationKey;
    if (!key)
      return await this.requestAnonymousToken();
    try {
      const res = await fetch(SP_ACCESS_TOKEN_URL, {
        method: "POST",
        headers: {
          "User-Agent": UA,
          Authorization: `Basic ${key}`,
          "Content-Type": "application/json"
        }
      });
      const body = await res.json();
      if (!body.access_token)
        throw "no token";
      const data = {
        token: body.access_token,
        expiresAfter: body.expires_in,
        type: "Bearer"
      };
      return this.accessToken = data;
    } catch {
      return await this.requestAnonymousToken();
    }
  }
  async requestAnonymousToken() {
    try {
      const res = await fetch(SP_ANON_TOKEN_URL, {
        headers: {
          "User-Agent": UA,
          "Content-Type": "application/json"
        }
      });
      if (!res.ok)
        throw "not_ok";
      const body = await res.json();
      if (!body.accessToken)
        throw "no_access_token";
      const data = {
        token: body.accessToken,
        expiresAfter: body.accessTokenExpirationTimestampMs,
        type: "Bearer"
      };
      return this.accessToken = data;
    } catch {
      return null;
    }
  }
  isTokenExpired() {
    if (!this.accessToken)
      return true;
    return Date.now() > this.accessToken.expiresAfter;
  }
  async search(query) {
    try {
      if (this.isTokenExpired())
        await this.requestToken();
      if (!this.accessToken)
        return null;
      const res = await fetch(`${SP_BASE}/search/?q=${encodeURIComponent(query)}&type=track&market=US`, {
        headers: {
          "User-Agent": UA,
          Authorization: `${this.accessToken.type} ${this.accessToken.token}`,
          "Content-Type": "application/json"
        }
      });
      if (!res.ok)
        return null;
      const data = await res.json();
      return data.tracks.items.map((m) => ({
        title: m.name,
        duration: m.duration_ms,
        artist: m.artists.map((m2) => m2.name).join(", "),
        url: m.external_urls?.spotify || `https://open.spotify.com/track/${m.id}`,
        thumbnail: m.album.images?.[0]?.url || null
      }));
    } catch {
      return null;
    }
  }
  async getPlaylist(id) {
    try {
      if (this.isTokenExpired())
        await this.requestToken();
      if (!this.accessToken)
        return null;
      const res = await fetch(`${SP_BASE}/playlists/${id}?market=US`, {
        headers: {
          "User-Agent": UA,
          Authorization: `${this.accessToken.type} ${this.accessToken.token}`,
          "Content-Type": "application/json"
        }
      });
      if (!res.ok)
        return null;
      const data = await res.json();
      if (!data.tracks.items.length)
        return null;
      const t = data.tracks.items;
      let next = data.tracks.next;
      while (typeof next === "string") {
        try {
          const res2 = await fetch(next, {
            headers: {
              "User-Agent": UA,
              Authorization: `${this.accessToken.type} ${this.accessToken.token}`,
              "Content-Type": "application/json"
            }
          });
          if (!res2.ok)
            break;
          const nextPage = await res2.json();
          t.push(...nextPage.items);
          next = nextPage.next;
          if (!next)
            break;
        } catch {
          break;
        }
      }
      const tracks = t.map(({ track: m }) => ({
        title: m.name,
        duration: m.duration_ms,
        artist: m.artists.map((m2) => m2.name).join(", "),
        url: m.external_urls?.spotify || `https://open.spotify.com/track/${m.id}`,
        thumbnail: m.album.images?.[0]?.url || null
      }));
      if (!tracks.length)
        return null;
      return {
        name: data.name,
        author: data.owner.display_name,
        thumbnail: data.images?.[0]?.url || null,
        id: data.id,
        url: data.external_urls.spotify || `https://open.spotify.com/playlist/${id}`,
        tracks
      };
    } catch {
      return null;
    }
  }
  async getAlbum(id) {
    try {
      if (this.isTokenExpired())
        await this.requestToken();
      if (!this.accessToken)
        return null;
      const res = await fetch(`${SP_BASE}/albums/${id}?market=US`, {
        headers: {
          "User-Agent": UA,
          Authorization: `${this.accessToken.type} ${this.accessToken.token}`,
          "Content-Type": "application/json"
        }
      });
      if (!res.ok)
        return null;
      const data = await res.json();
      if (!data.tracks.items.length)
        return null;
      const t = data.tracks.items;
      let next = data.tracks.next;
      while (typeof next === "string") {
        try {
          const res2 = await fetch(next, {
            headers: {
              "User-Agent": UA,
              Authorization: `${this.accessToken.type} ${this.accessToken.token}`,
              "Content-Type": "application/json"
            }
          });
          if (!res2.ok)
            break;
          const nextPage = await res2.json();
          t.push(...nextPage.items);
          next = nextPage.next;
          if (!next)
            break;
        } catch {
          break;
        }
      }
      const tracks = t.map((m) => ({
        title: m.name,
        duration: m.duration_ms,
        artist: m.artists.map((m2) => m2.name).join(", "),
        url: m.external_urls?.spotify || `https://open.spotify.com/track/${m.id}`,
        thumbnail: data.images?.[0]?.url || null
      }));
      if (!tracks.length)
        return null;
      return {
        name: data.name,
        author: data.artists.map((m) => m.name).join(", "),
        thumbnail: data.images?.[0]?.url || null,
        id: data.id,
        url: data.external_urls.spotify || `https://open.spotify.com/album/${id}`,
        tracks
      };
    } catch {
      return null;
    }
  }
};
__name(_SpotifyAPI, "SpotifyAPI");
var SpotifyAPI = _SpotifyAPI;

// src/extractors/BridgedExtractor.ts
import { BaseExtractor as BaseExtractor6 } from "discord-player";

// src/extractors/common/BridgeProvider.ts
var BridgeSource = /* @__PURE__ */ ((BridgeSource3) => {
  BridgeSource3["Auto"] = "auto";
  BridgeSource3["SoundCloud"] = "soundcloud";
  BridgeSource3["YouTube"] = "youtube";
  return BridgeSource3;
})(BridgeSource || {});
var _BridgeProvider = class _BridgeProvider {
  constructor(source) {
    this.bridgeSource = "soundcloud" /* SoundCloud */;
    this.setBridgeSource(source);
  }
  setBridgeSource(source) {
    switch (source) {
      case "soundcloud":
      case "soundcloud" /* SoundCloud */:
        this.bridgeSource = "soundcloud" /* SoundCloud */;
        break;
      case "youtube":
      case "youtube" /* YouTube */:
        this.bridgeSource = "youtube" /* YouTube */;
        break;
      case "auto":
      case "auto" /* Auto */:
        this.bridgeSource = "auto" /* Auto */;
        break;
      default:
        throw new TypeError("invalid bridge source");
    }
  }
  isSoundCloud() {
    return this.bridgeSource === "soundcloud" /* SoundCloud */;
  }
  isYouTube() {
    return this.bridgeSource === "youtube" /* YouTube */;
  }
  isAuto() {
    return this.bridgeSource === "auto" /* Auto */;
  }
  resolveProvider() {
    if (this.isAuto()) {
      if (YoutubeExtractor.instance && !isExtDisabled(YoutubeExtractor.instance)) {
        return "youtube" /* YouTube */;
      }
      if (SoundCloudExtractor.instance && !isExtDisabled(SoundCloudExtractor.instance)) {
        return "soundcloud" /* SoundCloud */;
      }
      throw new Error("Could not find any available extractors for automatic bridging.");
    }
    return this.bridgeSource;
  }
  async resolve(ext, track) {
    const isSoundcloud = this.resolveProvider() === "soundcloud" /* SoundCloud */;
    const bridgefn = isSoundcloud ? pullSCMetadata : pullYTMetadata;
    const oldQc = ext.createBridgeQuery;
    if (isSoundcloud)
      ext.createBridgeQuery = (track2) => `${track2.author} ${track2.title}`;
    const res = await bridgefn(ext, track);
    ext.debug(`Extracted bridge metadata using ${isSoundcloud ? "soundcloud" : "youtube"} extractor: ${JSON.stringify(res)}`);
    ext.createBridgeQuery = oldQc;
    return { source: isSoundcloud ? "soundcloud" : "youtube", data: res };
  }
  async stream(meta) {
    if (!meta.data)
      throw new Error("Could not find bridge metadata info.");
    if (meta.source === "soundcloud") {
      if (!SoundCloudExtractor.instance) {
        throw new Error("Could not find soundcloud extractor, make sure SoundCloudExtractor is instantiated properly.");
      }
      if (isExtDisabled(SoundCloudExtractor.instance)) {
        throw new Error("Cannot stream, SoundCloudExtractor is disabled.");
      }
      return await SoundCloudExtractor.instance.internal.util.streamLink(meta.data, "progressive");
    } else if (meta.source === "youtube") {
      if (!YoutubeExtractor.instance) {
        throw new Error("Could not find youtube extractor, make sure YouTubeExtractor is instantiated properly.");
      }
      if (isExtDisabled(YoutubeExtractor.instance)) {
        throw new Error("Cannot stream, YouTubeExtractor is disabled.");
      }
      return YoutubeExtractor.instance._stream(meta.data.url, YoutubeExtractor.instance, YoutubeExtractor.instance.supportsDemux);
    } else {
      throw new TypeError("invalid bridge source");
    }
  }
};
__name(_BridgeProvider, "BridgeProvider");
var BridgeProvider = _BridgeProvider;
function isExtDisabled(ext) {
  const streamBlocked = !!ext.context.player.options.blockStreamFrom?.some((x) => x === ext.identifier);
  return streamBlocked;
}
__name(isExtDisabled, "isExtDisabled");
var defaultBridgeProvider = new BridgeProvider("auto" /* Auto */);
var createBridgeProvider = /* @__PURE__ */ __name((source) => new BridgeProvider(source), "createBridgeProvider");

// src/extractors/BridgedExtractor.ts
var _BridgedExtractor = class _BridgedExtractor extends BaseExtractor6 {
  constructor(context, options) {
    super(context, options);
  }
  setBridgeProvider(provider) {
    this.options.bridgeProvider = provider;
  }
  setBridgeProviderSource(source) {
    this.bridgeProvider.setBridgeSource(source);
  }
  get bridgeProvider() {
    return this.options.bridgeProvider ?? defaultBridgeProvider;
  }
};
__name(_BridgedExtractor, "BridgedExtractor");
var BridgedExtractor = _BridgedExtractor;

// src/extractors/AppleMusicExtractor.ts
var _AppleMusicExtractor = class _AppleMusicExtractor extends BridgedExtractor {
  async activate() {
    this.protocols = ["amsearch", "applemusic"];
    const fn = this.options.createStream;
    if (typeof fn === "function") {
      this._stream = (q) => {
        return fn(this, q);
      };
    }
  }
  async deactivate() {
    this.protocols = [];
  }
  async validate(query, type) {
    return [
      QueryType6.APPLE_MUSIC_ALBUM,
      QueryType6.APPLE_MUSIC_PLAYLIST,
      QueryType6.APPLE_MUSIC_SONG,
      QueryType6.APPLE_MUSIC_SEARCH,
      QueryType6.AUTO,
      QueryType6.AUTO_SEARCH
    ].some((t) => t === type);
  }
  async getRelatedTracks(track, history) {
    if (track.queryType === QueryType6.APPLE_MUSIC_SONG) {
      const data = await this.handle(track.author || track.title, {
        type: QueryType6.APPLE_MUSIC_SEARCH,
        requestedBy: track.requestedBy
      });
      const unique = data.tracks.filter((t) => !history.tracks.some((h) => h.url === t.url));
      return unique.length > 0 ? this.createResponse(null, unique) : this.createResponse();
    }
    return this.createResponse();
  }
  async handle(query, context) {
    if (context.protocol === "amsearch")
      context.type = QueryType6.APPLE_MUSIC_SEARCH;
    switch (context.type) {
      case QueryType6.AUTO:
      case QueryType6.AUTO_SEARCH:
      case QueryType6.APPLE_MUSIC_SEARCH: {
        const data = await AppleMusic.search(query);
        if (!data || !data.length)
          return this.createResponse();
        const tracks = data.map(
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (m) => {
            const track = new Track6(this.context.player, {
              author: m.artist.name,
              description: m.title,
              duration: typeof m.duration === "number" ? Util6.buildTimeCode(Util6.parseMS(m.duration)) : m.duration,
              thumbnail: m.thumbnail,
              title: m.title,
              url: m.url,
              views: 0,
              source: "apple_music",
              requestedBy: context.requestedBy,
              queryType: "appleMusicSong",
              metadata: {
                source: m,
                bridge: null
              },
              requestMetadata: async () => {
                return {
                  source: m,
                  bridge: this.options.bridgeProvider ? (await this.options.bridgeProvider.resolve(this, track)).data : await pullYTMetadata(this, track)
                };
              }
            });
            track.extractor = this;
            return track;
          }
        );
        return this.createResponse(null, tracks);
      }
      case QueryType6.APPLE_MUSIC_ALBUM: {
        const info = await AppleMusic.getAlbumInfo(query);
        if (!info)
          return this.createResponse();
        const playlist = new Playlist3(this.context.player, {
          author: {
            name: info.artist.name,
            url: ""
          },
          description: info.title,
          id: info.id,
          source: "apple_music",
          thumbnail: info.thumbnail,
          title: info.title,
          tracks: [],
          type: "album",
          url: info.url,
          rawPlaylist: info
        });
        playlist.tracks = info.tracks.map(
          (m) => {
            const track = new Track6(this.context.player, {
              author: m.artist.name,
              description: m.title,
              duration: typeof m.duration === "number" ? Util6.buildTimeCode(Util6.parseMS(m.duration)) : m.duration,
              thumbnail: m.thumbnail,
              title: m.title,
              url: m.url,
              views: 0,
              source: "apple_music",
              requestedBy: context.requestedBy,
              queryType: "appleMusicSong",
              metadata: {
                source: info,
                bridge: null
              },
              requestMetadata: async () => {
                return {
                  source: info,
                  bridge: (await this.options.bridgeProvider?.resolve(this, track))?.data
                };
              }
            });
            track.playlist = playlist;
            track.extractor = this;
            return track;
          }
        );
        return { playlist, tracks: playlist.tracks };
      }
      case QueryType6.APPLE_MUSIC_PLAYLIST: {
        const info = await AppleMusic.getPlaylistInfo(query);
        if (!info)
          return this.createResponse();
        const playlist = new Playlist3(this.context.player, {
          author: {
            name: info.artist.name,
            url: ""
          },
          description: info.title,
          id: info.id,
          source: "apple_music",
          thumbnail: info.thumbnail,
          title: info.title,
          tracks: [],
          type: "playlist",
          url: info.url,
          rawPlaylist: info
        });
        playlist.tracks = info.tracks.map(
          (m) => {
            const track = new Track6(this.context.player, {
              author: m.artist.name,
              description: m.title,
              duration: typeof m.duration === "number" ? Util6.buildTimeCode(Util6.parseMS(m.duration)) : m.duration,
              thumbnail: m.thumbnail,
              title: m.title,
              url: m.url,
              views: 0,
              source: "apple_music",
              requestedBy: context.requestedBy,
              queryType: "appleMusicSong",
              metadata: {
                source: m,
                bridge: null
              },
              requestMetadata: async () => {
                return {
                  source: m,
                  bridge: this.options.bridgeProvider ? (await this.options.bridgeProvider.resolve(this, track)).data : await pullYTMetadata(this, track)
                };
              }
            });
            track.playlist = playlist;
            track.extractor = this;
            return track;
          }
        );
        return { playlist, tracks: playlist.tracks };
      }
      case QueryType6.APPLE_MUSIC_SONG: {
        const info = await AppleMusic.getSongInfo(query);
        if (!info)
          return this.createResponse();
        const track = new Track6(this.context.player, {
          author: info.artist.name,
          description: info.title,
          duration: typeof info.duration === "number" ? Util6.buildTimeCode(Util6.parseMS(info.duration)) : info.duration,
          thumbnail: info.thumbnail,
          title: info.title,
          url: info.url,
          views: 0,
          source: "apple_music",
          requestedBy: context.requestedBy,
          queryType: context.type,
          metadata: {
            source: info,
            bridge: null
          },
          requestMetadata: async () => {
            return {
              source: info,
              bridge: this.options.bridgeProvider ? (await this.options.bridgeProvider.resolve(this, track)).data : await pullYTMetadata(this, track)
            };
          }
        });
        track.extractor = this;
        return { playlist: null, tracks: [track] };
      }
      default:
        return { playlist: null, tracks: [] };
    }
  }
  async stream(info) {
    if (this._stream) {
      const stream = await this._stream(info.url, this);
      if (typeof stream === "string")
        return stream;
      return stream;
    }
    if (!("requestBridge" in this.context)) {
      const provider = this.bridgeProvider;
      if (!provider)
        throw new Error(`Could not find bridge provider for '${this.constructor.name}'`);
      const data = await provider.resolve(this, info);
      if (!data)
        throw new Error("Failed to bridge this track");
      info.setMetadata({
        ...info.metadata || {},
        bridge: data.data
      });
      return await provider.stream(data);
    }
    const result = await this.context.requestBridge(info, this);
    if (!result?.result)
      throw new Error("Could not bridge this track");
    return result.result;
  }
};
__name(_AppleMusicExtractor, "AppleMusicExtractor");
_AppleMusicExtractor.identifier = "com.discord-player.applemusicextractor";
var AppleMusicExtractor = _AppleMusicExtractor;

// src/extractors/SpotifyExtractor.ts
import { Playlist as Playlist4, QueryType as QueryType7, Track as Track7, Util as Util7 } from "discord-player";
import spotify from "spotify-url-info";
var re = /^(?:https:\/\/open\.spotify\.com\/(intl-([a-z]|[A-Z]){0,3}\/)?(?:user\/[A-Za-z0-9]+\/)?|spotify:)(album|playlist|track)(?:[/:])([A-Za-z0-9]+).*$/;
var _SpotifyExtractor = class _SpotifyExtractor extends BridgedExtractor {
  constructor() {
    super(...arguments);
    this._credentials = {
      clientId: this.options.clientId || process.env.DP_SPOTIFY_CLIENT_ID || null,
      clientSecret: this.options.clientSecret || process.env.DP_SPOTIFY_CLIENT_SECRET || null
    };
    this.internal = new SpotifyAPI(this._credentials);
  }
  async activate() {
    this.protocols = ["spsearch", "spotify"];
    this._lib = spotify(fetch);
    if (this.internal.isTokenExpired())
      await this.internal.requestToken();
    const fn = this.options.createStream;
    if (typeof fn === "function") {
      this._stream = (q) => {
        return fn(this, q);
      };
    }
  }
  async deactivate() {
    this._stream = void 0;
    this._lib = void 0;
    this.protocols = [];
  }
  async validate(query, type) {
    return [
      QueryType7.SPOTIFY_ALBUM,
      QueryType7.SPOTIFY_PLAYLIST,
      QueryType7.SPOTIFY_SONG,
      QueryType7.SPOTIFY_SEARCH,
      QueryType7.AUTO,
      QueryType7.AUTO_SEARCH
    ].some((t) => t === type);
  }
  async getRelatedTracks(track) {
    return await this.handle(track.author || track.title, {
      type: QueryType7.SPOTIFY_SEARCH,
      requestedBy: track.requestedBy
    });
  }
  async handle(query, context) {
    if (context.protocol === "spsearch")
      context.type = QueryType7.SPOTIFY_SEARCH;
    switch (context.type) {
      case QueryType7.AUTO:
      case QueryType7.AUTO_SEARCH:
      case QueryType7.SPOTIFY_SEARCH: {
        const data = await this.internal.search(query);
        if (!data)
          return this.createResponse();
        return this.createResponse(
          null,
          data.map((spotifyData) => {
            const track = new Track7(this.context.player, {
              title: spotifyData.title,
              description: `${spotifyData.title} by ${spotifyData.artist}`,
              author: spotifyData.artist ?? "Unknown Artist",
              url: spotifyData.url,
              thumbnail: spotifyData.thumbnail || "https://www.scdn.co/i/_global/twitter_card-default.jpg",
              duration: Util7.buildTimeCode(Util7.parseMS(spotifyData.duration ?? 0)),
              views: 0,
              requestedBy: context.requestedBy,
              source: "spotify",
              queryType: QueryType7.SPOTIFY_SONG,
              metadata: {
                source: spotifyData,
                bridge: null
              },
              requestMetadata: async () => {
                return {
                  source: spotifyData,
                  bridge: (await this.options.bridgeProvider?.resolve(this, track))?.data
                };
              }
            });
            track.extractor = this;
            return track;
          })
        );
      }
      case QueryType7.SPOTIFY_SONG: {
        const spotifyData = await this._lib.getData(query, context.requestOptions).catch(Util7.noop);
        if (!spotifyData)
          return { playlist: null, tracks: [] };
        const spotifyTrack = new Track7(this.context.player, {
          title: spotifyData.title,
          description: `${spotifyData.name} by ${spotifyData.artists.map((m) => m.name).join(", ")}`,
          author: spotifyData.artists[0]?.name ?? "Unknown Artist",
          url: spotifyData.id ? `https://open.spotify.com/track/${spotifyData.id}` : query,
          thumbnail: spotifyData.coverArt?.sources?.[0]?.url || "https://www.scdn.co/i/_global/twitter_card-default.jpg",
          duration: Util7.buildTimeCode(Util7.parseMS(spotifyData.duration ?? spotifyData.maxDuration ?? 0)),
          views: 0,
          requestedBy: context.requestedBy,
          source: "spotify",
          queryType: context.type,
          metadata: {
            source: spotifyData,
            bridge: null
          },
          requestMetadata: async () => {
            return {
              source: spotifyData,
              bridge: this.options.bridgeProvider ? (await this.options.bridgeProvider.resolve(this, spotifyTrack)).data : await pullYTMetadata(this, spotifyTrack)
            };
          }
        });
        spotifyTrack.extractor = this;
        return { playlist: null, tracks: [spotifyTrack] };
      }
      case QueryType7.SPOTIFY_PLAYLIST: {
        try {
          const { queryType, id } = this.parse(query);
          if (queryType !== "playlist")
            throw "err";
          const spotifyPlaylist = await this.internal.getPlaylist(id);
          if (!spotifyPlaylist)
            throw "err";
          const playlist = new Playlist4(this.context.player, {
            title: spotifyPlaylist.name,
            description: spotifyPlaylist.name ?? "",
            thumbnail: spotifyPlaylist.thumbnail ?? "https://www.scdn.co/i/_global/twitter_card-default.jpg",
            type: "playlist",
            source: "spotify",
            author: {
              name: spotifyPlaylist.author ?? "Unknown Artist",
              url: null
            },
            tracks: [],
            id: spotifyPlaylist.id,
            url: spotifyPlaylist.url || query,
            rawPlaylist: spotifyPlaylist
          });
          playlist.tracks = spotifyPlaylist.tracks.map((spotifyData) => {
            const data = new Track7(this.context.player, {
              title: spotifyData.title,
              description: `${spotifyData.title} by ${spotifyData.artist}`,
              author: spotifyData.artist ?? "Unknown Artist",
              url: spotifyData.url,
              thumbnail: spotifyData.thumbnail || "https://www.scdn.co/i/_global/twitter_card-default.jpg",
              duration: Util7.buildTimeCode(Util7.parseMS(spotifyData.duration ?? 0)),
              views: 0,
              requestedBy: context.requestedBy,
              source: "spotify",
              queryType: QueryType7.SPOTIFY_SONG,
              metadata: {
                source: spotifyData,
                bridge: null
              },
              requestMetadata: async () => {
                return {
                  source: spotifyData,
                  bridge: this.options.bridgeProvider ? (await this.options.bridgeProvider.resolve(this, data)).data : await pullYTMetadata(this, data)
                };
              }
            });
            data.extractor = this;
            data.playlist = playlist;
            return data;
          });
          return { playlist, tracks: playlist.tracks };
        } catch {
          const spotifyPlaylist = await this._lib.getData(query, context.requestOptions).catch(Util7.noop);
          if (!spotifyPlaylist)
            return { playlist: null, tracks: [] };
          const playlist = new Playlist4(this.context.player, {
            title: spotifyPlaylist.name ?? spotifyPlaylist.title,
            description: spotifyPlaylist.title ?? "",
            thumbnail: spotifyPlaylist.coverArt?.sources?.[0]?.url ?? "https://www.scdn.co/i/_global/twitter_card-default.jpg",
            type: spotifyPlaylist.type,
            source: "spotify",
            author: {
              name: spotifyPlaylist.subtitle ?? "Unknown Artist",
              url: null
            },
            tracks: [],
            id: spotifyPlaylist.id,
            url: spotifyPlaylist.id ? `https://open.spotify.com/playlist/${spotifyPlaylist.id}` : query,
            rawPlaylist: spotifyPlaylist
          });
          playlist.tracks = spotifyPlaylist.trackList.map((m) => {
            const data = new Track7(this.context.player, {
              title: m.title ?? "",
              description: m.title ?? "",
              author: m.subtitle ?? "Unknown Artist",
              url: m.uid ? `https://open.spotify.com/tracks/${m.uid}` : query,
              thumbnail: "https://www.scdn.co/i/_global/twitter_card-default.jpg",
              duration: Util7.buildTimeCode(Util7.parseMS(m.duration)),
              views: 0,
              requestedBy: context.requestedBy,
              playlist,
              source: "spotify",
              queryType: "spotifySong",
              metadata: {
                source: m,
                bridge: null
              },
              requestMetadata: async () => {
                return {
                  source: m,
                  bridge: this.options.bridgeProvider ? (await this.options.bridgeProvider.resolve(this, data)).data : await pullYTMetadata(this, data)
                };
              }
            });
            data.extractor = this;
            data.playlist = playlist;
            return data;
          });
          return { playlist, tracks: playlist.tracks };
        }
      }
      case QueryType7.SPOTIFY_ALBUM: {
        try {
          const { queryType, id } = this.parse(query);
          if (queryType !== "album")
            throw "err";
          const spotifyAlbum = await this.internal.getAlbum(id);
          if (!spotifyAlbum)
            throw "err";
          const playlist = new Playlist4(this.context.player, {
            title: spotifyAlbum.name,
            description: spotifyAlbum.name ?? "",
            thumbnail: spotifyAlbum.thumbnail ?? "https://www.scdn.co/i/_global/twitter_card-default.jpg",
            type: "album",
            source: "spotify",
            author: {
              name: spotifyAlbum.author ?? "Unknown Artist",
              url: null
            },
            tracks: [],
            id: spotifyAlbum.id,
            url: spotifyAlbum.url || query,
            rawPlaylist: spotifyAlbum
          });
          playlist.tracks = spotifyAlbum.tracks.map((spotifyData) => {
            const data = new Track7(this.context.player, {
              title: spotifyData.title,
              description: `${spotifyData.title} by ${spotifyData.artist}`,
              author: spotifyData.artist ?? "Unknown Artist",
              url: spotifyData.url,
              thumbnail: spotifyData.thumbnail || "https://www.scdn.co/i/_global/twitter_card-default.jpg",
              duration: Util7.buildTimeCode(Util7.parseMS(spotifyData.duration ?? 0)),
              views: 0,
              requestedBy: context.requestedBy,
              source: "spotify",
              queryType: QueryType7.SPOTIFY_SONG,
              metadata: {
                source: spotifyData,
                bridge: null
              },
              requestMetadata: async () => {
                return {
                  source: spotifyData,
                  bridge: this.options.bridgeProvider ? (await this.options.bridgeProvider.resolve(this, data)).data : await pullYTMetadata(this, data)
                };
              }
            });
            data.extractor = this;
            data.playlist = playlist;
            return data;
          });
          return { playlist, tracks: playlist.tracks };
        } catch {
          const album = await this._lib.getData(query, context.requestOptions).catch(Util7.noop);
          if (!album)
            return { playlist: null, tracks: [] };
          const playlist = new Playlist4(this.context.player, {
            title: album.name ?? album.title,
            description: album.title ?? "",
            thumbnail: album.coverArt?.sources?.[0]?.url ?? "https://www.scdn.co/i/_global/twitter_card-default.jpg",
            type: album.type,
            source: "spotify",
            author: {
              name: album.subtitle ?? "Unknown Artist",
              url: null
            },
            tracks: [],
            id: album.id,
            url: album.id ? `https://open.spotify.com/playlist/${album.id}` : query,
            rawPlaylist: album
          });
          playlist.tracks = album.trackList.map((m) => {
            const data = new Track7(this.context.player, {
              title: m.title ?? "",
              description: m.title ?? "",
              author: m.subtitle ?? "Unknown Artist",
              url: m.uid ? `https://open.spotify.com/tracks/${m.uid}` : query,
              thumbnail: "https://www.scdn.co/i/_global/twitter_card-default.jpg",
              duration: Util7.buildTimeCode(Util7.parseMS(m.duration)),
              views: 0,
              requestedBy: context.requestedBy,
              playlist,
              source: "spotify",
              queryType: "spotifySong",
              metadata: {
                source: m,
                bridge: null
              },
              requestMetadata: async () => {
                return {
                  source: m,
                  bridge: this.options.bridgeProvider ? (await this.options.bridgeProvider.resolve(this, data)).data : await pullYTMetadata(this, data)
                };
              }
            });
            data.extractor = this;
            data.playlist = playlist;
            return data;
          });
          return { playlist, tracks: playlist.tracks };
        }
      }
      default:
        return { playlist: null, tracks: [] };
    }
  }
  async stream(info) {
    if (this._stream) {
      const stream = await this._stream(info.url, this);
      if (typeof stream === "string")
        return stream;
      return stream;
    }
    if (!("requestBridge" in this.context)) {
      const provider = this.bridgeProvider;
      if (!provider)
        throw new Error(`Could not find bridge provider for '${this.constructor.name}'`);
      const data = await provider.resolve(this, info);
      if (!data)
        throw new Error("Failed to bridge this track");
      info.setMetadata({
        ...info.metadata || {},
        bridge: data.data
      });
      return await provider.stream(data);
    }
    const result = await this.context.requestBridge(info, this);
    if (!result?.result)
      throw new Error("Could not bridge this track");
    return result.result;
  }
  parse(q) {
    const [, , , queryType, id] = re.exec(q) || [];
    return { queryType, id };
  }
};
__name(_SpotifyExtractor, "SpotifyExtractor");
_SpotifyExtractor.identifier = "com.discord-player.spotifyextractor";
var SpotifyExtractor = _SpotifyExtractor;

// src/index.ts
var version = "4.5.1";
export {
  AppleMusicExtractor,
  AttachmentExtractor,
  BridgeProvider,
  BridgeSource,
  BridgedExtractor,
  internal_exports as Internal,
  ReverbnationExtractor,
  SoundCloudExtractor,
  SpotifyExtractor,
  UA,
  VimeoExtractor,
  YoutubeExtractor as YouTubeExtractor,
  YouTubeLibs,
  YoutubeExtractor,
  createBridgeProvider,
  createImport,
  defaultBridgeProvider,
  fetch,
  filterSoundCloudPreviews,
  loadYtdl,
  lyricsExtractor,
  makeSCSearch,
  makeYTSearch,
  pullSCMetadata,
  pullYTMetadata,
  version
};
//# sourceMappingURL=data:application/json;base64,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