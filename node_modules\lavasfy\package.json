{"name": "lavasfy", "version": "2.3.0", "description": "Spotify album, playlist, and track resolver for Lavalink.", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "tsc", "lint": "eslint --ignore-path .gitignore \"**/*.ts\"", "lint:fix": "yarn lint --fix", "test": "yarn lint && yarn build", "release": "standard-version", "docs": "typedoc"}, "repository": "https://github.com/Allvaa/lava-spotify", "author": "Allvaa", "license": "MIT", "dependencies": {"node-superfetch": "^0.2.2"}, "devDependencies": {"@types/node": "^15.12.2", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "eslint": "^7.17.0", "standard-version": "^9.1.0", "typedoc": "^0.20.20", "typescript": "^4.3.2"}}