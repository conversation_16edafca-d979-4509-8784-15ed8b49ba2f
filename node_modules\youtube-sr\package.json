{"name": "youtube-sr", "version": "4.3.12", "description": "Simple package to make YouTube search.", "files": ["dist"], "main": "dist/mod.js", "types": "dist/mod.d.ts", "module": "dist/mod.mjs", "scripts": {"build": "bun run build:node && bun run build:deno", "prepublishOnly": "bun run build", "build:node": "tsup", "build:deno": "node scripts/deno.js", "format": "prettier --write \"**/*.{js,ts}\""}, "repository": {"type": "git", "url": "git+https://github.com/twlite/youtube-sr.git"}, "keywords": ["youtube", "api", "search", "playlist", "channel", "video", "scrape", "ytsr", "ytpl", "yt-search", "youtube-search", "ytdl", "youtube-dl", "node", "deno", "scrape-youtube", "youtube-scrape", "youtube-api", "simple-youtube-api", "bun", "bun.sh", "bun-youtube", "bun-youtube-api", "buntube", "youtube-bun"], "author": "twlite", "license": "Apache-2.0", "bugs": {"url": "https://github.com/twlite/youtube-sr/issues"}, "homepage": "https://github.com/twlite/youtube-sr#readme", "devDependencies": {"@types/node": "^24.0.10", "chalk": "^5.3.0", "cross-fetch": "^4.1.0", "fs-extra": "^11.3.0", "prettier": "^3.6.2", "readdirp": "^3.6.0", "rimraf": "^5.0.5", "tsup": "^8.0.2", "tsx": "^4.7.2", "typescript": "^5.4.5"}}