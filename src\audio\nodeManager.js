const { Shoukaku, Connectors } = require('shoukaku');
const logger = require('../utils/logger');

class NodeManager {
    constructor(bot) {
        this.bot = bot;
        this.shoukaku = null;
        this.nodes = new Map();
        this.stats = {
            totalNodes: 0,
            connectedNodes: 0,
            totalPlayers: 0,
            averageLoad: 0,
            uptime: 0
        };
        
        this.initialize();
    }

    async initialize() {
        try {
            // Check if Lavalink is enabled
            if (this.bot.config.lavalink.enabled === false || this.bot.config.lavalink.nodes.length === 0) {
                logger.info('🔇 Lavalink disabled in NodeManager');
                return;
            }

            this.shoukaku = new Shoukaku(
                new Connectors.DiscordJS(this.bot),
                this.bot.config.lavalink.nodes,
                {
                    moveOnDisconnect: false,
                    resumable: true,
                    resumableTimeout: 30,
                    reconnectTries: 2,
                    restTimeout: 10000,
                    userAgent: 'Ultra-Premium Music Bot (https://github.com/yourusername/ultra-premium-music-bot)'
                }
            );

            this.setupEventHandlers();
            logger.success('✅ NodeManager initialized');

        } catch (error) {
            logger.error('❌ Error initializing NodeManager:', error);
            throw error;
        }
    }

    setupEventHandlers() {
        this.shoukaku.on('ready', () => {
            logger.success('✅ Shoukaku connected to Lavalink nodes');
            this.updateStats();
        });

        this.shoukaku.on('error', (name, error) => {
            logger.error(`Shoukaku error on node ${name}:`, error);
            this.bot.emit('nodeError', name, error);
        });

        this.shoukaku.on('close', (name, code, reason) => {
            logger.warn(`Shoukaku node ${name} closed: ${code} - ${reason}`);
            this.bot.emit('nodeClose', name, code, reason);
        });

        this.shoukaku.on('disconnect', (name, players, moved) => {
            logger.warn(`Shoukaku node ${name} disconnected, ${players.length} players affected, ${moved.length} moved`);
            this.bot.emit('nodeDisconnect', name, players, moved);
        });

        this.shoukaku.on('debug', (name, info) => {
            logger.debug(`Shoukaku debug ${name}:`, info);
        });

        this.shoukaku.on('nodeConnect', (name) => {
            logger.success(`✅ Connected to Lavalink node: ${name}`);
            this.nodes.set(name, { connected: true, lastConnected: Date.now() });
            this.updateStats();
        });

        this.shoukaku.on('nodeDisconnect', (name) => {
            logger.warn(`⚠️ Disconnected from Lavalink node: ${name}`);
            this.nodes.set(name, { connected: false, lastDisconnected: Date.now() });
            this.updateStats();
        });

        this.shoukaku.on('nodeReconnect', (name) => {
            logger.info(`🔄 Reconnecting to Lavalink node: ${name}`);
        });
    }

    async addNode(nodeConfig) {
        try {
            const { name, url, auth, region, secure = false } = nodeConfig;
            
            // Add node to config
            this.bot.config.lavalink.nodes.push({
                name,
                url,
                auth,
                region,
                secure
            });

            // Reconnect Shoukaku with new config
            await this.shoukaku.addNode({
                name,
                url,
                auth,
                region,
                secure
            });

            logger.success(`✅ Added Lavalink node: ${name}`);
            this.updateStats();

        } catch (error) {
            logger.error(`Error adding node ${nodeConfig.name}:`, error);
            throw error;
        }
    }

    async removeNode(nodeName) {
        try {
            const node = this.shoukaku.nodes.get(nodeName);
            if (!node) {
                throw new Error(`Node ${nodeName} not found`);
            }

            // Disconnect node
            await node.disconnect();

            // Remove from config
            this.bot.config.lavalink.nodes = this.bot.config.lavalink.nodes.filter(
                n => n.name !== nodeName
            );

            logger.success(`✅ Removed Lavalink node: ${nodeName}`);
            this.updateStats();

        } catch (error) {
            logger.error(`Error removing node ${nodeName}:`, error);
            throw error;
        }
    }

    async connectNode(nodeName) {
        try {
            const node = this.shoukaku.nodes.get(nodeName);
            if (!node) {
                throw new Error(`Node ${nodeName} not found`);
            }

            await node.connect();
            logger.success(`✅ Connected to Lavalink node: ${nodeName}`);
            this.updateStats();

        } catch (error) {
            logger.error(`Error connecting to node ${nodeName}:`, error);
            throw error;
        }
    }

    async disconnectNode(nodeName) {
        try {
            const node = this.shoukaku.nodes.get(nodeName);
            if (!node) {
                throw new Error(`Node ${nodeName} not found`);
            }

            await node.disconnect();
            logger.success(`✅ Disconnected from Lavalink node: ${nodeName}`);
            this.updateStats();

        } catch (error) {
            logger.error(`Error disconnecting from node ${nodeName}:`, error);
            throw error;
        }
    }

    async restartNode(nodeName) {
        try {
            const node = this.shoukaku.nodes.get(nodeName);
            if (!node) {
                throw new Error(`Node ${nodeName} not found`);
            }

            await node.disconnect();
            await node.connect();

            logger.success(`✅ Restarted Lavalink node: ${nodeName}`);
            this.updateStats();

        } catch (error) {
            logger.error(`Error restarting node ${nodeName}:`, error);
            throw error;
        }
    }

    getBestNode() {
        try {
            const nodes = this.shoukaku.nodes;
            const connectedNodes = Array.from(nodes.values()).filter(node => node.state === 2); // CONNECTED state

            if (connectedNodes.length === 0) {
                throw new Error('No connected Lavalink nodes available');
            }

            // Find node with least load
            let bestNode = connectedNodes[0];
            let lowestLoad = this.calculateNodeLoad(bestNode);

            for (const node of connectedNodes) {
                const load = this.calculateNodeLoad(node);
                if (load < lowestLoad) {
                    lowestLoad = load;
                    bestNode = node;
                }
            }

            return bestNode;

        } catch (error) {
            logger.error('Error getting best node:', error);
            throw error;
        }
    }

    calculateNodeLoad(node) {
        try {
            if (!node.stats) return 100;

            const cpuLoad = node.stats.cpu ? node.stats.cpu.systemLoad : 100;
            const memoryLoad = node.stats.memory ? (node.stats.memory.used / node.stats.memory.reservable) * 100 : 100;
            const playerLoad = node.stats.playingPlayers / (node.stats.maxPlayers || 100) * 100;

            // Weighted average: CPU 40%, Memory 30%, Players 30%
            return (cpuLoad * 0.4) + (memoryLoad * 0.3) + (playerLoad * 0.3);

        } catch (error) {
            logger.error('Error calculating node load:', error);
            return 100;
        }
    }

    getNodeStats(nodeName) {
        try {
            const node = this.shoukaku.nodes.get(nodeName);
            if (!node) {
                throw new Error(`Node ${nodeName} not found`);
            }

            return {
                name: node.name,
                connected: node.state === 2,
                stats: node.stats,
                region: node.region,
                penalty: node.penalty,
                load: this.calculateNodeLoad(node)
            };

        } catch (error) {
            logger.error(`Error getting stats for node ${nodeName}:`, error);
            throw error;
        }
    }

    getAllNodeStats() {
        const nodes = this.shoukaku.nodes;
        const nodeStats = {};

        nodes.forEach((node, name) => {
            nodeStats[name] = {
                name: node.name,
                connected: node.state === 2,
                stats: node.stats,
                region: node.region,
                penalty: node.penalty,
                load: this.calculateNodeLoad(node)
            };
        });

        return nodeStats;
    }

    updateStats() {
        try {
            const nodes = this.shoukaku.nodes;
            const connectedNodes = Array.from(nodes.values()).filter(node => node.state === 2);
            
            let totalPlayers = 0;
            let totalLoad = 0;

            connectedNodes.forEach(node => {
                if (node.stats) {
                    totalPlayers += node.stats.playingPlayers;
                    totalLoad += this.calculateNodeLoad(node);
                }
            });

            this.stats = {
                totalNodes: nodes.size,
                connectedNodes: connectedNodes.length,
                totalPlayers: totalPlayers,
                averageLoad: connectedNodes.length > 0 ? totalLoad / connectedNodes.length : 0,
                uptime: this.bot.uptime
            };

        } catch (error) {
            logger.error('Error updating node stats:', error);
        }
    }

    getStats() {
        return { ...this.stats };
    }

    async healthCheck() {
        try {
            const nodes = this.shoukaku.nodes;
            const connectedNodes = Array.from(nodes.values()).filter(node => node.state === 2);
            
            if (connectedNodes.length === 0) {
                return {
                    healthy: false,
                    message: 'No connected Lavalink nodes',
                    nodes: this.getAllNodeStats()
                };
            }

            // Check if at least one node has low load
            const healthyNodes = connectedNodes.filter(node => {
                const load = this.calculateNodeLoad(node);
                return load < 80;
            });

            return {
                healthy: healthyNodes.length > 0,
                message: healthyNodes.length > 0 ? 'System healthy' : 'All nodes under high load',
                nodes: this.getAllNodeStats()
            };

        } catch (error) {
            logger.error('Error performing health check:', error);
            return {
                healthy: false,
                message: `Health check failed: ${error.message}`,
                nodes: this.getAllNodeStats()
            };
        }
    }

    async distributeLoad() {
        try {
            const nodes = this.shoukaku.nodes;
            const connectedNodes = Array.from(nodes.values()).filter(node => node.state === 2);

            if (connectedNodes.length === 0) {
                throw new Error('No connected Lavalink nodes available');
            }

            // Get all active players
            const allPlayers = [];
            connectedNodes.forEach(node => {
                if (node.players) {
                    allPlayers.push(...Array.from(node.players.values()));
                }
            });

            // Redistribute players based on node load
            const targetLoad = 50; // Target 50% load per node
            const redistributionPlan = this.calculateRedistribution(connectedNodes, allPlayers, targetLoad);

            // Execute redistribution
            for (const plan of redistributionPlan) {
                if (plan.moves.length > 0) {
                    logger.info(`🔄 Redistributing ${plan.moves.length} players from ${plan.fromNode} to ${plan.toNode}`);
                    
                    for (const move of plan.moves) {
                        try {
                            await this.movePlayer(move.player, plan.toNode);
                        } catch (error) {
                            logger.error(`Error moving player ${move.player.guildId}:`, error);
                        }
                    }
                }
            }

            logger.success('✅ Load redistribution completed');
            this.updateStats();

        } catch (error) {
            logger.error('Error redistributing load:', error);
            throw error;
        }
    }

    calculateRedistribution(nodes, players, targetLoad) {
        const redistributionPlan = [];
        
        // Calculate current load for each node
        const nodeLoads = nodes.map(node => ({
            node,
            load: this.calculateNodeLoad(node),
            players: Array.from(node.players.values())
        }));

        // Sort nodes by load (highest to lowest)
        nodeLoads.sort((a, b) => b.load - a.load);

        // Find nodes that need to offload players
        const overloadedNodes = nodeLoads.filter(n => n.load > targetLoad);
        const underloadedNodes = nodeLoads.filter(n => n.load < targetLoad);

        // Create redistribution plan
        for (const overloaded of overloadedNodes) {
            for (const underloaded of underloadedNodes) {
                if (overloaded.load <= targetLoad || underloaded.load >= targetLoad) break;

                const playersToMove = Math.min(
                    Math.floor((overloaded.load - targetLoad) / 10), // Rough estimate
                    overloaded.players.length
                );

                if (playersToMove > 0) {
                    const moves = overloaded.players.slice(0, playersToMove).map(player => ({
                        player,
                        guildId: player.guildId
                    }));

                    redistributionPlan.push({
                        fromNode: overloaded.node.name,
                        toNode: underloaded.node.name,
                        moves
                    });

                    // Update estimated loads
                    overloaded.load -= playersToMove * 10;
                    underloaded.load += playersToMove * 10;
                }
            }
        }

        return redistributionPlan;
    }

    async movePlayer(player, targetNode) {
        try {
            // This is a simplified version - actual implementation would depend on Shoukaku's API
            // For now, we'll just log the move
            logger.info(`Moving player from ${player.node.name} to ${targetNode.name}`);
            
            // In a real implementation, you would:
            // 1. Save current player state
            // 2. Disconnect from current node
            // 3. Connect to target node
            // 4. Restore player state

        } catch (error) {
            logger.error('Error moving player:', error);
            throw error;
        }
    }

    async createPlayer(guild, voiceChannel, textChannel) {
        try {
            const bestNode = this.getBestNode();
            const player = await bestNode.joinVoiceChannel({
                guildId: guild.id,
                channelId: voiceChannel.id,
                shardId: guild.shardId
            });

            logger.playerCreate(guild, voiceChannel.name);
            this.updateStats();

            return player;

        } catch (error) {
            logger.error('Error creating player:', error);
            throw error;
        }
    }

    async destroyPlayer(guild) {
        try {
            const nodes = this.shoukaku.nodes;
            
            for (const node of nodes.values()) {
                if (node.state === 2) { // Connected
                    const player = node.players.get(guild.id);
                    if (player) {
                        await player.disconnect();
                        logger.playerDestroy(guild);
                        this.updateStats();
                        return;
                    }
                }
            }

            throw new Error(`Player not found for guild ${guild.id}`);

        } catch (error) {
            logger.error('Error destroying player:', error);
            throw error;
        }
    }

    getPlayer(guild) {
        try {
            const nodes = this.shoukaku.nodes;
            
            for (const node of nodes.values()) {
                if (node.state === 2) { // Connected
                    const player = node.players.get(guild.id);
                    if (player) {
                        return player;
                    }
                }
            }

            return null;

        } catch (error) {
            logger.error('Error getting player:', error);
            return null;
        }
    }
}

// Singleton instance
let nodeManagerInstance = null;

const getNodeManager = (bot) => {
    if (!nodeManagerInstance) {
        nodeManagerInstance = new NodeManager(bot);
    }
    return nodeManagerInstance;
};

module.exports = {
    NodeManager,
    getNodeManager
};
