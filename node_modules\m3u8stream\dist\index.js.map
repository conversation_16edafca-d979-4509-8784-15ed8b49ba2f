{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,mCAAqC;AACrC,sDAA8B;AAC9B,gEAAuC;AACvC,wEAA8C;AAC9C,mCAA0C;AAC1C,6CAAwC;AAqCxC,MAAM,gBAAgB,GAAG;IACvB,IAAI,EAAE,qBAAU;IAChB,UAAU,EAAE,yBAAa;CAC1B,CAAC;AAEF,IAAI,UAAU,GAAG,CAAC,CAAC,WAAmB,EAAE,UAA8B,EAAE,EAAqB,EAAE;IAC7F,MAAM,MAAM,GAAG,IAAI,oBAAW,CAAC,EAAE,aAAa,EAAE,OAAO,CAAC,aAAa,EAAE,CAAsB,CAAC;IAC9F,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,CAAC,CAAC;IACnD,cAAc;IACd,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC;IAC/C,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAC9C,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IACtG,IAAI,CAAC,MAAM,EAAE;QACX,MAAM,SAAS,CAAC,WAAW,OAAO,CAAC,MAAM,iBAAiB,CAAC,CAAC;KAC7D;IACD,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,WAAW,EAAE;QACxC,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC;YACzC,qBAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC,CAAC,CAAC;KAC3C;IAED,MAAM,aAAa,GAAG,CAAC,GAAmB,EAAE,EAAE;QAC5C,KAAK,IAAI,KAAK,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE;YACpF,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;SAChD;IACH,CAAC,CAAC;IAEF,IAAI,WAAkC,CAAC;IACvC,MAAM,WAAW,GAAG,IAAI,aAAK,CAAC,CAAC,GAAmB,EAAE,QAAQ,EAAQ,EAAE;QACpE,WAAW,GAAG,GAAG,CAAC;QAClB,oEAAoE;QACpE,gBAAgB;QAChB,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;QACxD,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;QACjC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC5C,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;IAEvB,IAAI,aAAa,GAAG,CAAC,CAAC;IACtB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,MAAM,YAAY,GAAG,IAAI,aAAK,CAAC,CAAC,OAAa,EAAE,QAAkB,EAAQ,EAAE;QACzE,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;QACnD,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,OAAO,EAAE;gBACzD,KAAK,EAAE,SAAS,OAAO,CAAC,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;aAC3D,CAAC,CAAC;SACJ;QACD,IAAI,GAAG,GAAG,iBAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,QAAQ,EAAE,EAAE,UAAU,CAAC,CAAC;QAC5E,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC1B,aAAa,CAAC,GAAG,CAAC,CAAC;QACnB,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;YAChC,UAAU,IAAI,CAAC,IAAI,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;gBACtB,GAAG,EAAE,EAAE,aAAa;gBACpB,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,GAAG,EAAE,OAAO,CAAC,GAAG;aACjB,EAAE,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YACnC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC,CAAC;IAEpC,MAAM,OAAO,GAAG,CAAC,GAAU,EAAQ,EAAE;QACnC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC1B,qBAAqB;QACrB,MAAM,CAAC,GAAG,EAAE,CAAC;IACf,CAAC,CAAC;IAEF,gCAAgC;IAChC,IAAI,gBAAwB,CAAC;IAC7B,IAAI,cAAsB,CAAC;IAC3B,IAAI,cAA4B,CAAC;IACjC,IAAI,gBAAgB,GAAG,IAAI,CAAC;IAC5B,IAAI,KAAK,GAAG,KAAK,CAAC;IAClB,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,WAAmB,CAAC;IAExB,MAAM,WAAW,GAAG,CAAC,GAAiB,EAAQ,EAAE;QAC9C,WAAW,GAAG,IAAI,CAAC;QACnB,IAAI,GAAG,EAAE;YACP,OAAO,CAAC,GAAG,CAAC,CAAC;SACd;aAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ;YACjD,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,gBAAgB,EAAE;YACrE,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC;YAClE,gBAAgB,GAAG,IAAI,CAAC;YACxB,cAAc,GAAG,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;SAClD;aAAM,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC;YAC5B,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC;SACd;IACH,CAAC,CAAC;IAEF,IAAI,YAAmC,CAAC;IACxC,IAAI,OAAe,CAAC;IACpB,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,MAAM,eAAe,GAAG,GAAS,EAAE;QACjC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACzB,YAAY,GAAG,iBAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QACpD,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAClC,aAAa,CAAC,YAAY,CAAC,CAAC;QAC5B,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACzD,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAS,EAAE,EAAE;YACnC,IAAI,SAAS,EAAE;gBAAE,OAAO;aAAE;YAC1B,SAAS,GAAG,CAAC,CAAC;YACd,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,CAAC,EAAE;gBACnD,KAAK,IAAI,SAAS,CAAC;aACpB;QACH,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;QAEtE,IAAI,UAAU,GAAU,EAAE,CAAC;QAC3B,MAAM,OAAO,GAAG,CAAC,IAAe,EAAQ,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACd,IAAI,IAAI,CAAC,GAAG,IAAI,OAAO,EAAE;oBAAE,OAAO;iBAAE;gBACpC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;aACpB;YACD,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;YAClB,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACrC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC,CAAC;QAEF,IAAI,WAAW,GAAgB,EAAE,EAAE,mBAAmB,GAAG,CAAC,CAAC;QAC3D,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAU,EAAE,EAAE;YAC/B,IAAI,SAAS,mBAAK,IAAI,EAAE,SAAS,IAAK,IAAI,CAAE,CAAC;YAC7C,IAAI,KAAK,IAAI,SAAS,CAAC,IAAI,EAAE;gBAC3B,OAAO,CAAC,SAAS,CAAC,CAAC;aACpB;iBAAM;gBACL,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC5B,mBAAmB,IAAI,SAAS,CAAC,QAAQ,CAAC;gBAC1C,4CAA4C;gBAC5C,OAAO,WAAW,CAAC,MAAM,GAAG,CAAC;oBAC3B,mBAAmB,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,UAAU,EAAE;oBAC5D,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,EAAe,CAAC;oBAClD,mBAAmB,IAAI,QAAQ,CAAC,QAAQ,CAAC;iBAC1C;aACF;YACD,SAAS,IAAI,SAAS,CAAC,QAAQ,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACpB,YAAY,GAAG,IAAI,CAAC;YACpB,0DAA0D;YAC1D,8CAA8C;YAC9C,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,EAAE;gBAC5C,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjD;YAED,wDAAwD;YACxD,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;YAEpE,8DAA8D;YAC9D,uCAAuC;YACvC,cAAc;gBACZ,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;YAE/D,gBAAgB,GAAG,KAAK,CAAC;YACzB,WAAW,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACF,eAAe,EAAE,CAAC;IAElB,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;QAChB,KAAK,GAAG,IAAI,CAAC;QACb,WAAW,CAAC,GAAG,EAAE,CAAC;QAClB,YAAY,CAAC,GAAG,EAAE,CAAC;QACnB,YAAY,CAAC,cAAc,CAAC,CAAC;QAC7B,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,GAAG;QACxB,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,OAAO,GAAG;QACvB,oBAAW,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7C,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC,CAA8B,CAAC;AAChC,UAAU,CAAC,cAAc,GAAG,qBAAQ,CAAC;AAErC,iBAAS,UAAU,CAAC"}