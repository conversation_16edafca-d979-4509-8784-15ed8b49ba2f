var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// src/biquad/Coefficients.ts
var FilterType = {
  SinglePoleLowPassApprox: 0,
  SinglePoleLowPass: 1,
  LowPass: 2,
  HighPass: 3,
  BandPass: 4,
  Notch: 5,
  AllPass: 6,
  LowShelf: 7,
  HighShelf: 8,
  PeakingEQ: 9
};
var Q_BUTTERWORTH = Math.SQRT1_2;
var _Coefficients = class _Coefficients {
  constructor(data) {
    // Denominator coefficients
    this.a1 = 0;
    this.a2 = 0;
    // Nominator coefficients
    this.b0 = 0;
    this.b1 = 0;
    this.b2 = 0;
    if (data) {
      this.a1 = data.a1;
      this.a2 = data.a2;
      this.b0 = data.b0;
      this.b1 = data.b1;
      this.b2 = data.b2;
    }
  }
  static from(filter, samplingFreq, cutoffFreq, Q, dbGain = -10) {
    if (2 * cutoffFreq > samplingFreq) {
      throw new Error(`Cutoff frequency is too big!`);
    }
    if (Q < 0) {
      throw new Error(`Q may not be negative`);
    }
    const omega = 2 * Math.PI * cutoffFreq / samplingFreq;
    const bqf = typeof filter === "string" ? FilterType[filter] : filter;
    switch (bqf) {
      case FilterType.SinglePoleLowPassApprox: {
        const alpha = omega / (omega + 1);
        return new _Coefficients({
          a1: alpha - 1,
          a2: 0,
          b0: alpha,
          b1: 0,
          b2: 0
        });
      }
      case FilterType.SinglePoleLowPass: {
        const omega_t = Math.tan(omega / 2);
        const a0 = 1 + omega_t;
        return new _Coefficients({
          a1: (omega_t - 1) / a0,
          a2: 0,
          b0: omega_t / a0,
          b1: omega_t / a0,
          b2: 0
        });
      }
      case FilterType.LowPass: {
        const omega_s = Math.sin(omega);
        const omega_c = Math.cos(omega);
        const alpha = omega_s / (2 * Q);
        const b0 = (1 - omega_c) * 0.5;
        const b1 = 1 - omega_c;
        const b2 = (1 - omega_c) * 0.5;
        const a0 = 1 + alpha;
        const a1 = -2 * omega_c;
        const a2 = 1 - alpha;
        const div = 1 / a0;
        return new _Coefficients({
          a1: a1 * div,
          a2: a2 * div,
          b0: b0 * div,
          b1: b1 * div,
          b2: b2 * div
        });
      }
      case FilterType.HighPass: {
        const omega_s = Math.sin(omega);
        const omega_c = Math.cos(omega);
        const alpha = omega_s / (2 * Q);
        const b0 = (1 + omega_c) * 0.5;
        const b1 = -(1 + omega_c);
        const b2 = (1 + omega_c) * 0.5;
        const a0 = 1 + alpha;
        const a1 = -2 * omega_c;
        const a2 = 1 - alpha;
        const div = 1 / a0;
        return new _Coefficients({
          a1: a1 * div,
          a2: a2 * div,
          b0: b0 * div,
          b1: b1 * div,
          b2: b2 * div
        });
      }
      case FilterType.Notch: {
        const omega_s = Math.sin(omega);
        const omega_c = Math.cos(omega);
        const alpha = omega_s / (2 * Q);
        const b0 = 1;
        const b1 = -2 * omega_c;
        const b2 = 1;
        const a0 = 1 + alpha;
        const a1 = -2 * omega_c;
        const a2 = 1 - alpha;
        const div = 1 / a0;
        return new _Coefficients({
          a1: a1 * div,
          a2: a2 * div,
          b0: b0 * div,
          b1: b1 * div,
          b2: b2 * div
        });
      }
      case FilterType.BandPass: {
        const omega_s = Math.sin(omega);
        const omega_c = Math.cos(omega);
        const alpha = omega_s / (2 * Q);
        const b0 = omega_s / 2;
        const b1 = 0;
        const b2 = -(omega_s / 2);
        const a0 = 1 + alpha;
        const a1 = -2 * omega_c;
        const a2 = 1 - alpha;
        const div = 1 / a0;
        return new _Coefficients({
          a1: a1 * div,
          a2: a2 * div,
          b0: b0 * div,
          b1: b1 * div,
          b2: b2 * div
        });
      }
      case FilterType.AllPass: {
        const omega_s = Math.sin(omega);
        const omega_c = Math.cos(omega);
        const alpha = omega_s / (2 * Q);
        const b0 = 1 - alpha;
        const b1 = -2 * omega_c;
        const b2 = 1 + alpha;
        const a0 = 1 + alpha;
        const a1 = -2 * omega_c;
        const a2 = 1 - alpha;
        return new _Coefficients({
          a1: a1 / a0,
          a2: a2 / a0,
          b0: b0 / a0,
          b1: b1 / a0,
          b2: b2 / a0
        });
      }
      case FilterType.LowShelf: {
        const a = Math.pow(10, dbGain / 40);
        const omega_s = Math.sin(omega);
        const omega_c = Math.cos(omega);
        const alpha = omega_s / (2 * Q);
        const b0 = a * (a + 1 - (a - 1) * omega_c + 2 * alpha * Math.sqrt(a));
        const b1 = 2 * a * (a - 1 - (a + 1) * omega_c);
        const b2 = a * (a + 1 - (a - 1) * omega_c - 2 * alpha * Math.sqrt(a));
        const a0 = a + 1 + (a - 1) * omega_c + 2 * alpha * Math.sqrt(a);
        const a1 = -2 * (a - 1 + (a + 1) * omega_c);
        const a2 = a + 1 + (a - 1) * omega_c - 2 * alpha * Math.sqrt(a);
        return new _Coefficients({
          a1: a1 / a0,
          a2: a2 / a0,
          b0: b0 / a0,
          b1: b1 / a0,
          b2: b2 / a0
        });
      }
      case FilterType.HighShelf: {
        const a = Math.pow(10, dbGain / 40);
        const omega_s = Math.sin(omega);
        const omega_c = Math.cos(omega);
        const alpha = omega_s / (2 * Q);
        const b0 = a * (a + 1 + (a - 1) * omega_c + 2 * alpha * Math.sqrt(a));
        const b1 = -2 * a * (a - 1 + (a + 1) * omega_c);
        const b2 = a * (a + 1 + (a - 1) * omega_c - 2 * alpha * Math.sqrt(a));
        const a0 = a + 1 - (a - 1) * omega_c + 2 * alpha * Math.sqrt(a);
        const a1 = 2 * (a - 1 - (a + 1) * omega_c);
        const a2 = a + 1 - (a - 1) * omega_c - 2 * alpha * Math.sqrt(a);
        return new _Coefficients({
          a1: a1 / a0,
          a2: a2 / a0,
          b0: b0 / a0,
          b1: b1 / a0,
          b2: b2 / a0
        });
      }
      case FilterType.PeakingEQ: {
        const a = Math.pow(10, dbGain / 40);
        const omega_s = Math.sin(omega);
        const omega_c = Math.cos(omega);
        const alpha = omega_s / (2 * Q);
        const b0 = 1 + alpha * a;
        const b1 = -2 * omega_c;
        const b2 = 1 - alpha * a;
        const a0 = 1 + alpha / a;
        const a1 = -2 * omega_c;
        const a2 = 1 - alpha / a;
        return new _Coefficients({
          a1: a1 / a0,
          a2: a2 / a0,
          b0: b0 / a0,
          b1: b1 / a0,
          b2: b2 / a0
        });
      }
      default:
        throw new TypeError(`Invalid filter type "${filter}"`);
    }
  }
};
__name(_Coefficients, "Coefficients");
var Coefficients = _Coefficients;

// src/biquad/Biquad.ts
var _BiquadFilter = class _BiquadFilter {
  constructor(coefficients) {
    this.coefficients = coefficients;
    this.x1 = 0;
    this.x2 = 0;
    this.y1 = 0;
    this.y2 = 0;
    this.s1 = 0;
    this.s2 = 0;
  }
  setFilter(filter, options) {
    const coefficients = Coefficients.from(filter, options.fs, options.f0, options.Q, options.gain);
    this.update(coefficients);
  }
  update(coefficients) {
    this.coefficients = coefficients;
  }
  replace(coefficients) {
    this.coefficients = coefficients;
  }
  reset() {
    this.x1 = 0;
    this.x2 = 0;
    this.y1 = 0;
    this.y2 = 0;
    this.s1 = 0;
    this.s2 = 0;
  }
  run(input) {
    const { a1, a2, b0, b1, b2 } = this.coefficients;
    const out = b0 * input + b1 * this.x1 + b2 * this.x2 - a1 * this.y1 - a2 * this.y2;
    this.x2 = this.x1;
    this.x1 = input;
    this.y2 = this.y1;
    this.y1 = out;
    return out;
  }
  runTransposed(input) {
    const { a1, a2, b0, b1, b2 } = this.coefficients;
    const out = this.s1 + b0 * input;
    this.s1 = this.s2 + b1 * input - a1 * out;
    this.s2 = b2 * input - a2 * out;
    return out;
  }
};
__name(_BiquadFilter, "BiquadFilter");
var BiquadFilter = _BiquadFilter;

// src/utils/Frequency.ts
var _Frequency = class _Frequency {
  constructor(__val) {
    this.__val = __val;
    if (typeof __val !== "number" || isNaN(__val) || __val === Infinity)
      throw new TypeError("Frequency value must be a number");
    if (this.__val < 0)
      throw new Error(`Frequency value cannot be negative (${__val})`);
  }
  khz() {
    return this.__val * 1e3;
  }
  mhz() {
    return this.__val * 1e6;
  }
  hz() {
    return this.__val;
  }
  dt() {
    return 1 / this.__val;
  }
  valueOf() {
    return this.__val;
  }
  toString() {
    return `${this.__val}Hz`;
  }
  toJSON() {
    return this.toString();
  }
};
__name(_Frequency, "Frequency");
var Frequency = _Frequency;

// src/utils/PCMTransformer.ts
import { Transform } from "stream";
var _PCMTransformer = class _PCMTransformer extends Transform {
  constructor(options = {}) {
    super(options);
    this.type = "s16le";
    this.disabled = false;
    this.sampleRate = 48e3;
    this.onUpdate = /* @__PURE__ */ __name(() => {
    }, "onUpdate");
    options.type ?? (options.type = "s16le");
    this.disabled = !!options.disabled;
    if (typeof options.sampleRate === "number" && options.sampleRate > 0) {
      this.sampleRate = options.sampleRate;
    }
    switch (options.type) {
      case "s16be":
      case "s16le":
        this.type = options.type;
        this.bits = 16;
        break;
      case "s32be":
      case "s32le":
        this.type = options.type;
        this.bits = 32;
        break;
      default:
        throw new TypeError(`Expected type to be one of ${["s16be", "s16le", "s32be", "s32le"].join(", ")}, got "${options.type}"`);
    }
    this.bytes = this.bits / 8;
    this.extremum = Math.pow(2, this.bits - 1);
  }
  disable() {
    this.disabled = true;
  }
  enable() {
    this.disabled = false;
  }
  toggle() {
    this.disabled = !this.disabled;
  }
  _readInt(buffer, index) {
    const method = `readInt${this.type.substring(1).toUpperCase()}`;
    return buffer[method](index);
  }
  _writeInt(buffer, int, index) {
    const method = `writeInt${this.type.substring(1).toUpperCase()}`;
    return buffer[method](int, index);
  }
  clamp(val, max = this.extremum - 1, min = -this.extremum) {
    return Math.min(max, Math.max(min, val));
  }
  setSampleRate(rate) {
    this.sampleRate = rate;
    return;
  }
};
__name(_PCMTransformer, "PCMTransformer");
var PCMTransformer = _PCMTransformer;

// src/biquad/BiquadStream.ts
var _BiquadStream = class _BiquadStream extends PCMTransformer {
  constructor(options = {}) {
    super(options);
    this.cutoff = 80;
    this.gain = 0;
    this.Q = Q_BUTTERWORTH;
    if ("cutoff" in options)
      this.cutoff = options.cutoff;
    if ("gain" in options)
      this.gain = options.gain;
    if ("Q" in options)
      this.Q = options.Q;
    if ("biquadFilter" in options) {
      if (typeof options.biquadFilter === "string" || typeof options.biquadFilter === "number")
        this.biquadFilter = options.filter;
      if (this.biquadFilter != null) {
        this.biquad = new BiquadFilter(Coefficients.from(this.biquadFilter, this.sampleRate, this.cutoff, this.Q, this.gain));
      }
    }
  }
  get filter() {
    return this.biquadFilter;
  }
  set filter(f) {
    if (f == null || typeof f === "string" || typeof f === "number") {
      this.update({ filter: f });
    } else {
      throw new TypeError(`Invalid biquad filter type "${f}"`);
    }
  }
  getFilterName() {
    if (this.biquadFilter == null)
      return null;
    if (typeof this.biquadFilter === "string")
      return this.biquadFilter;
    return Object.entries(FilterType).find((r) => r[1] === this.biquadFilter)?.[0];
  }
  update(options) {
    if ("cutoff" in options)
      this.cutoff = options.cutoff;
    if ("gain" in options)
      this.gain = options.gain;
    if ("Q" in options)
      this.Q = options.Q;
    if ("filter" in options)
      this.biquadFilter = options.filter;
    if (this.biquadFilter != null) {
      this.biquad = new BiquadFilter(Coefficients.from(this.biquadFilter, this.sampleRate, this.cutoff, this.Q, this.gain));
    }
    this.onUpdate?.();
  }
  setFilter(filter) {
    this.update({ filter });
  }
  setQ(Q) {
    this.update({ Q });
  }
  setCutoff(f0) {
    this.update({ cutoff: f0 });
  }
  setGain(dB) {
    this.update({ gain: dB });
  }
  _transform(chunk, encoding, callback) {
    if (this.disabled || !this.biquad) {
      this.push(chunk);
      return callback();
    }
    const endIndex = Math.floor(chunk.length / 2) * 2;
    const { bytes } = this;
    for (let sampleIndex = 0; sampleIndex < endIndex; sampleIndex += bytes) {
      const int = this._readInt(chunk, sampleIndex);
      const result = this.biquad.run(int);
      this._writeInt(chunk, this.clamp(result), sampleIndex);
    }
    this.push(chunk);
    return callback();
  }
};
__name(_BiquadStream, "BiquadStream");
var BiquadStream = _BiquadStream;

// src/equalizer/ChannelProcessor.ts
var _ChannelProcessor = class _ChannelProcessor {
  constructor(bandMultipliers) {
    this.history = new Array(Equalizer.BAND_COUNT * 6).fill(0);
    this.bandMultipliers = bandMultipliers;
    this.current = 0;
    this.m1 = 2;
    this.m2 = 1;
  }
  processInt(int) {
    let result = int * 0.25;
    for (let bandIndex = 0; bandIndex < Equalizer.BAND_COUNT; bandIndex++) {
      const x = bandIndex * 6;
      const y = x + 3;
      const coefficients = Equalizer.Coefficients48000[bandIndex];
      const bandResult = coefficients.alpha * (int - this.history[x + this.m2]) + coefficients.gamma * this.history[y + this.m1] - coefficients.beta * this.history[y + this.m2];
      this.history[x + this.current] = int;
      this.history[y + this.current] = bandResult;
      result += bandResult * this.bandMultipliers[bandIndex];
    }
    const val = result * 4;
    return val;
  }
  process(samples, extremum = 131072, bytes = 2, readInt, writeInt) {
    const endIndex = Math.floor(samples.length / 2) * 2;
    for (let sampleIndex = 0; sampleIndex < endIndex; sampleIndex += bytes) {
      const sample = readInt?.(samples, sampleIndex) ?? samples.readInt16LE(sampleIndex);
      const result = this.processInt(sample);
      const val = Math.min(extremum - 1, Math.max(-extremum, result));
      writeInt?.(samples, val, sampleIndex) ?? samples.writeInt16LE(val, sampleIndex);
      this.step();
    }
    return samples;
  }
  step() {
    if (++this.current === 3) {
      this.current = 0;
    }
    if (++this.m1 === 3) {
      this.m1 = 0;
    }
    if (++this.m2 === 3) {
      this.m2 = 0;
    }
  }
  reset() {
    this.history.fill(0);
  }
};
__name(_ChannelProcessor, "ChannelProcessor");
var ChannelProcessor = _ChannelProcessor;

// src/equalizer/Coefficients.ts
var _EqualizerCoefficients = class _EqualizerCoefficients {
  constructor(beta, alpha, gamma) {
    this.beta = beta;
    this.alpha = alpha;
    this.gamma = gamma;
  }
  setBeta(v) {
    this.beta = v;
  }
  setAlpha(v) {
    this.alpha = v;
  }
  setGamma(v) {
    this.gamma = v;
  }
  toJSON() {
    const { alpha, beta, gamma } = this;
    return { alpha, beta, gamma };
  }
};
__name(_EqualizerCoefficients, "EqualizerCoefficients");
var EqualizerCoefficients = _EqualizerCoefficients;

// src/equalizer/EqualizerConfiguration.ts
var _EqualizerConfiguration = class _EqualizerConfiguration {
  constructor(bandMultipliers) {
    this.bandMultipliers = bandMultipliers;
  }
  setGain(band, value) {
    if (this.isValidBand(band)) {
      this.bandMultipliers[band] = Math.max(Math.min(value, 1), -0.25);
    }
  }
  getGain(band) {
    if (this.isValidBand(band)) {
      return this.bandMultipliers[band];
    } else {
      return 0;
    }
  }
  isValidBand(band) {
    return band >= 0 && band < this.bandMultipliers.length;
  }
};
__name(_EqualizerConfiguration, "EqualizerConfiguration");
var EqualizerConfiguration = _EqualizerConfiguration;

// src/equalizer/Equalizer.ts
var _Equalizer = class _Equalizer extends EqualizerConfiguration {
  constructor(channelCount, bandMultipliers) {
    super(bandMultipliers);
    this.channels = [];
    this.channelCount = channelCount;
    this.channels = this.createChannelProcessor();
  }
  createChannelProcessor() {
    return Array.from({ length: this.channelCount }, () => {
      return new ChannelProcessor(this.bandMultipliers);
    });
  }
  process(input) {
    return this.channels.map((c, i) => {
      const { data, extremum, readInt, writeInt, bytes } = input[i];
      return c.process(data, extremum, bytes, readInt, writeInt);
    });
  }
};
__name(_Equalizer, "Equalizer");
_Equalizer.BAND_COUNT = 15;
_Equalizer.SAMPLE_RATE = 48e3;
_Equalizer.Coefficients48000 = [
  new EqualizerCoefficients(0.99847546664, 76226668143e-14, 1.9984647656),
  new EqualizerCoefficients(0.99756184654, 0.0012190767289, 1.9975344645),
  new EqualizerCoefficients(0.99616261379, 0.0019186931041, 1.9960947369),
  new EqualizerCoefficients(0.99391578543, 0.0030421072865, 1.9937449618),
  new EqualizerCoefficients(0.99028307215, 0.0048584639242, 1.9898465702),
  new EqualizerCoefficients(0.98485897264, 0.0075705136795, 1.9837962543),
  new EqualizerCoefficients(0.97588512657, 0.012057436715, 1.9731772447),
  new EqualizerCoefficients(0.96228521814, 0.018857390928, 1.9556164694),
  new EqualizerCoefficients(0.94080933132, 0.029595334338, 1.9242054384),
  new EqualizerCoefficients(0.90702059196, 0.046489704022, 1.8653476166),
  new EqualizerCoefficients(0.85868004289, 0.070659978553, 1.7600401337),
  new EqualizerCoefficients(0.78409610788, 0.10795194606, 1.5450725522),
  new EqualizerCoefficients(0.68332861002, 0.15833569499, 1.1426447155),
  new EqualizerCoefficients(0.55267518228, 0.22366240886, 0.40186190803),
  new EqualizerCoefficients(0.41811888447, 0.29094055777, -0.70905944223)
];
var Equalizer = _Equalizer;

// src/equalizer/EqualizerStream.ts
var _EqualizerStream = class _EqualizerStream extends PCMTransformer {
  constructor(options) {
    super(options);
    this.bandMultipliers = new Array(Equalizer.BAND_COUNT).fill(0);
    options = Object.assign(
      {},
      {
        bandMultiplier: [],
        channels: 1
      },
      options || {}
    );
    this.equalizer = new Equalizer(options.channels || 1, this.bandMultipliers);
    if (Array.isArray(options.bandMultiplier))
      this._processBands(options.bandMultiplier);
  }
  _processBands(multiplier) {
    for (const mul of multiplier) {
      if (mul.band > Equalizer.BAND_COUNT - 1 || mul.band < 0)
        throw new RangeError(`Band value out of range. Expected >0 & <${Equalizer.BAND_COUNT - 1}, received "${mul.band}"`);
      this.equalizer.setGain(mul.band, mul.gain);
    }
    this.onUpdate?.();
  }
  _transform(chunk, encoding, callback) {
    if (this.disabled) {
      this.push(chunk);
      return callback();
    }
    this.equalizer.process([
      {
        data: chunk,
        extremum: this.extremum,
        readInt: (b, idx) => this._readInt(b, idx),
        writeInt: (b, i, idx) => this._writeInt(b, i, idx),
        bytes: this.bytes
      }
    ]);
    this.push(chunk);
    return callback();
  }
  getEQ() {
    return this.bandMultipliers.map((m, i) => ({
      band: i,
      gain: m
    }));
  }
  setEQ(bands) {
    this._processBands(bands);
  }
  resetEQ() {
    this._processBands(
      Array.from(
        {
          length: Equalizer.BAND_COUNT
        },
        (_, i) => ({
          band: i,
          gain: 0
        })
      )
    );
  }
};
__name(_EqualizerStream, "EqualizerStream");
var EqualizerStream = _EqualizerStream;

// src/audio/MonoStereoTransformer.ts
var _MonoStereoTransformer = class _MonoStereoTransformer extends PCMTransformer {
  constructor(options) {
    super(options);
    if (!["m2s", "s2m"].includes(options?.strategy)) {
      throw new TypeError(`Strategy must be "m2s" or "s2m"`);
    }
    this.strategy = options.strategy;
  }
  setStrategy(strategy) {
    this.strategy = strategy;
  }
  _transform(chunk, encoding, callback) {
    if (this.disabled) {
      this.push(chunk);
      return callback();
    }
    const len = Math.floor(chunk.length / 2) * 2;
    if (this.strategy === "m2s") {
      this.push(this.toStereo(chunk, len));
    } else {
      this.push(this.toMono(chunk, len));
    }
    return callback();
  }
  toStereo(sample, len) {
    const bytes = this.bytes;
    const stereoBuffer = Buffer.alloc(len * 2);
    for (let i = 0; i < len; i += bytes) {
      stereoBuffer[i * 2 + 0] = sample[i];
      stereoBuffer[i * 2 + 1] = sample[i + 1];
      stereoBuffer[i * 2 + 2] = sample[i];
      stereoBuffer[i * 2 + 3] = sample[i + 1];
    }
    return stereoBuffer;
  }
  toMono(sample, len) {
    const bytes = this.bytes;
    const monoBuffer = Buffer.alloc(Math.floor(len / 2));
    for (let i = 0; i < len; i += bytes) {
      monoBuffer[i] = sample[i * 2 + 0];
      monoBuffer[i + 1] = sample[i * 2 + 1];
    }
    return monoBuffer;
  }
};
__name(_MonoStereoTransformer, "MonoStereoTransformer");
var MonoStereoTransformer = _MonoStereoTransformer;

// src/audio/transformers/index.ts
var transformers_exports = {};
__export(transformers_exports, {
  applyBiquad: () => applyBiquad,
  applyEqualization: () => applyEqualization,
  applyPulsator: () => applyPulsator,
  applyTremolo: () => applyTremolo,
  applyVibrato: () => applyVibrato,
  applyVolume: () => applyVolume
});

// src/audio/transformers/biquad.ts
function applyBiquad(filterer, int) {
  return filterer.run(int);
}
__name(applyBiquad, "applyBiquad");

// src/audio/transformers/dsp.ts
function applyPulsator(config, int, channel) {
  const sin = Math.sin(config.x);
  const currentChannelVal = channel === 0 ? sin : -sin;
  const res = int * (currentChannelVal + 1) / 2;
  config.x += config.dI;
  return res;
}
__name(applyPulsator, "applyPulsator");
function applyTremolo(config, int, sampleRate) {
  const fOffset = 1 - config.depth;
  const modSignal = fOffset + config.depth * Math.sin(config.phase);
  config.phase += 2 * Math.PI / sampleRate * config.frequency;
  return modSignal * int;
}
__name(applyTremolo, "applyTremolo");
function applyVibrato(config, int, sampleRate) {
  const fOffset = 1 - config.depth;
  const modSignal = fOffset + config.depth * Math.sin(2 * Math.PI * config.phase);
  config.phase += 2 * Math.PI / sampleRate * config.frequency;
  return modSignal * int;
}
__name(applyVibrato, "applyVibrato");
function applyVolume(vol, int) {
  return vol * int;
}
__name(applyVolume, "applyVolume");

// src/audio/transformers/equalizer.ts
function applyEqualization(eq, int) {
  const processor = eq.channels[0];
  const result = processor.processInt(int);
  processor.step();
  return result;
}
__name(applyEqualization, "applyEqualization");

// src/audio/AudioFilter.ts
var AudioFilters = {
  "8D": "8D",
  Tremolo: "Tremolo",
  Vibrato: "Vibrato"
};
var AF_NIGHTCORE_RATE = 1.3;
var AF_VAPORWAVE_RATE = 0.8;
var BASS_EQ_BANDS = Array.from({ length: 3 }, (_, i) => ({
  band: i,
  gain: 0.25
}));
var _AudioFilter = class _AudioFilter extends PCMTransformer {
  constructor(options) {
    super(options);
    this.filters = [];
    this.targetSampleRate = this.sampleRate;
    this.totalSamples = 0;
    this._processedSamples = 0;
    this.pulsatorConfig = {
      hz: 0.02,
      x: 0,
      dI: 3926990816987241e-21
    };
    this.tremoloConfig = {
      phase: 0,
      depth: 0.5,
      frequency: 5
    };
    this.vibratoConfig = {
      phase: 0,
      depth: 0.5,
      frequency: 5
    };
    if (options && Array.isArray(options.filters)) {
      this.setFilters(options.filters);
    }
    this.onUpdate?.();
  }
  setTargetSampleRate(rate) {
    this.targetSampleRate = rate || this.sampleRate;
    return;
  }
  setPulsator(hz) {
    hz /= 4;
    this.pulsatorConfig.hz = hz;
    const samplesPerCycle = this.targetSampleRate / (hz * 2 * Math.PI);
    this.pulsatorConfig.dI = hz === 0 ? 0 : 1 / samplesPerCycle;
    this.onUpdate?.();
  }
  get pulsator() {
    return this.pulsatorConfig.hz;
  }
  setTremolo({ depth = this.tremoloConfig.depth, frequency = this.tremoloConfig.frequency, phase = this.tremoloConfig.phase }) {
    if (typeof depth === "number")
      this.tremoloConfig.depth = depth;
    if (typeof frequency === "number")
      this.tremoloConfig.frequency = frequency;
    if (typeof phase === "number")
      this.tremoloConfig.phase = phase;
    this.onUpdate?.();
  }
  setVibrato({ depth = this.vibratoConfig.depth, frequency = this.vibratoConfig.frequency, phase = this.vibratoConfig.phase }) {
    if (typeof depth === "number")
      this.vibratoConfig.depth = depth;
    if (typeof frequency === "number")
      this.vibratoConfig.frequency = frequency;
    if (typeof phase === "number")
      this.vibratoConfig.phase = phase;
    this.onUpdate?.();
  }
  get tremolo() {
    return this.tremoloConfig;
  }
  setFilters(filters) {
    if (!Array.isArray(filters) || !filters.every((r) => r in AudioFilters)) {
      return false;
    }
    this.filters = filters;
    this.onUpdate?.();
    return true;
  }
  // TODO
  seek(duration) {
    throw new Error("Not Implemented");
  }
  _transform(chunk, encoding, callback) {
    this._processedSamples++;
    this.totalSamples += chunk.length / this.bits;
    if (this.disabled || !this.filters.length) {
      return callback(null, chunk);
    }
    const len = Math.floor(chunk.length / 2) * 2;
    const { bytes } = this;
    let L = false;
    for (let i = 0; i < len; i += bytes) {
      const int = this._readInt(chunk, i);
      const value = this.applyFilters(int, +(L = !L));
      this._writeInt(chunk, this.clamp(value), i);
    }
    this.push(chunk);
    return callback();
  }
  get currentSampleRate() {
    return this.targetSampleRate || this.sampleRate;
  }
  get estimatedDuration() {
    return this.totalSamples / this.targetSampleRate * 1e3;
  }
  get currentDuration() {
    return this._processedSamples * 1e3 / this.targetSampleRate;
  }
  applyFilters(byte, channel) {
    if (this.filters.length) {
      for (const filter of this.filters) {
        if (filter === "8D") {
          byte = applyPulsator(this.pulsatorConfig, byte, channel);
        }
        if (filter === "Tremolo") {
          byte = applyTremolo(this.tremoloConfig, byte, this.currentSampleRate);
        }
        if (filter === "Vibrato") {
          byte = applyVibrato(this.vibratoConfig, byte, this.currentSampleRate);
        }
      }
    }
    return byte;
  }
};
__name(_AudioFilter, "AudioFilter");
var AudioFilter = _AudioFilter;

// src/audio/PCMResampler.ts
var _PCMResampler = class _PCMResampler extends PCMTransformer {
  constructor(options) {
    super(options);
    this.targetSampleRate = this.sampleRate;
    if (options?.targetSampleRate)
      this.targetSampleRate = options.targetSampleRate;
  }
  get AF_NIGHTCORE() {
    return 64e3;
  }
  get AF_VAPORWAVE() {
    return 32e3;
  }
  setTargetSampleRate(rate) {
    if (rate === "NIGHTCORE" || rate === "VAPORWAVE")
      rate = this[`AF_${rate}`];
    if (typeof rate !== "number")
      return false;
    this.targetSampleRate = rate;
    this.onUpdate?.();
    return true;
  }
  // TODO: enable this
  _transform(chunk, _, cb) {
    if (this.disabled || this.sampleRate === this.targetSampleRate) {
      this.push(chunk);
      return cb();
    }
    this.push(chunk);
    cb();
  }
};
__name(_PCMResampler, "PCMResampler");
var PCMResampler = _PCMResampler;

// src/audio/VolumeTransformer.ts
var _VolumeTransformer = class _VolumeTransformer extends PCMTransformer {
  constructor(options) {
    super(options);
    this._volume = 1;
    if (typeof options?.volume === "number") {
      this.setVolume(options.volume);
    }
  }
  get volumeApprox() {
    return this._volume * 100;
  }
  get volume() {
    return Math.floor(this.volumeApprox);
  }
  set volume(volume) {
    this.setVolume(volume);
  }
  setVolume(volume) {
    if (typeof volume !== "number" || isNaN(volume))
      throw new Error(`Expected volume amount to be a number, received ${typeof volume}!`);
    if (volume < 0)
      volume = 0;
    if (!isFinite(volume))
      volume = 100;
    this._volume = volume / 100;
    this.onUpdate?.();
    return true;
  }
  _transform(chunk, encoding, callback) {
    if (this.disabled || this._volume === 1) {
      this.push(chunk);
      return callback();
    }
    const len = Math.floor(chunk.length / 2) * 2;
    const { bytes } = this;
    for (let i = 0; i < len; i += bytes) {
      const int = this._readInt(chunk, i);
      const amp = this.clamp(int * this._volume);
      this._writeInt(chunk, amp, i);
    }
    this.push(chunk);
    return callback();
  }
  toString() {
    return `${this.volume}%`;
  }
};
__name(_VolumeTransformer, "VolumeTransformer");
var VolumeTransformer = _VolumeTransformer;

// src/FiltersChainBuilder.ts
import { pipeline } from "stream";
var _FiltersChain = class _FiltersChain {
  constructor(presets = {}) {
    this.presets = presets;
    this.equalizer = null;
    this.filters = null;
    this.biquad = null;
    this.volume = null;
    this.resampler = null;
    this.destination = null;
    this.source = null;
    this.onUpdate = /* @__PURE__ */ __name(() => null, "onUpdate");
    this.onError = /* @__PURE__ */ __name(() => null, "onError");
  }
  create(src, presets = this.presets) {
    this.destroy();
    this.source = src;
    const equalizerStream = !presets.equalizer?.disabled ? new EqualizerStream(presets.equalizer) : null;
    const dspStream = !presets.dsp?.disabled ? new AudioFilter(presets.dsp) : null;
    const biquadStream = !presets.biquad?.disabled ? new BiquadStream(presets.biquad) : null;
    const volumeTransformer = !presets.volume?.disabled ? new VolumeTransformer(presets.volume) : null;
    this.equalizer = equalizerStream;
    this.filters = dspStream;
    this.biquad = biquadStream;
    this.volume = volumeTransformer;
    if (equalizerStream)
      equalizerStream.onUpdate = this.onUpdate;
    if (dspStream)
      dspStream.onUpdate = this.onUpdate;
    if (biquadStream)
      biquadStream.onUpdate = this.onUpdate;
    if (volumeTransformer)
      volumeTransformer.onUpdate = this.onUpdate;
    const chains = [src, equalizerStream, dspStream, biquadStream, volumeTransformer].filter(Boolean);
    if (!chains.length)
      return src;
    this.destination = pipeline(...chains, (err) => {
      if (err) {
        this.destroy();
        if (!err.message.includes("ERR_STREAM_PREMATURE_CLOSE"))
          this.onError(err);
      }
    });
    this.destination.once("close", this.destroy.bind(this));
    return this.destination;
  }
  destroy() {
    this.equalizer?.destroy();
    this.biquad?.destroy();
    this.filters?.destroy();
    this.volume?.destroy();
    this.destination?.destroy();
    this.source?.destroy();
    this.equalizer?.removeAllListeners();
    this.biquad?.removeAllListeners();
    this.filters?.removeAllListeners();
    this.volume?.removeAllListeners();
    this.destination?.removeAllListeners();
    this.source?.removeAllListeners();
    this.equalizer = null;
    this.biquad = null;
    this.filters = null;
    this.volume = null;
    this.destination = null;
    this.source = null;
  }
};
__name(_FiltersChain, "FiltersChain");
var FiltersChain = _FiltersChain;

// src/index.ts
var version = "0.2.3";
export {
  AF_NIGHTCORE_RATE,
  AF_VAPORWAVE_RATE,
  AudioFilter,
  AudioFilters,
  BASS_EQ_BANDS,
  BiquadFilter,
  BiquadStream,
  ChannelProcessor,
  Coefficients,
  Equalizer,
  EqualizerCoefficients,
  EqualizerConfiguration,
  EqualizerStream,
  FilterType,
  FiltersChain,
  Frequency,
  MonoStereoTransformer,
  transformers_exports as PCMAudioTransformer,
  PCMResampler,
  PCMTransformer,
  Q_BUTTERWORTH,
  VolumeTransformer,
  version
};
//# sourceMappingURL=data:application/json;base64,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